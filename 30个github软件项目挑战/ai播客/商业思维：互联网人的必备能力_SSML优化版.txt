<?xml version="1.0" encoding="UTF-8"?>
<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
    <voice name="zh-CN-XiaoxiaoNeural">
        <audio src="https://example.com/light-background-music.mp3" />
        欢迎收听豆包AI播客节目我。Hello, 大家好，欢迎收听我们这一期播客。
        <prosody rate="fast" volume="loud">
            然后咱们今天来聊一聊为什么互联网行业的朋友应该都学习一些商业思维，以及我们如何通过一些具体的方法来构建自己的商业思维体系。
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        <audio src="https://example.com/thinking-sound.mp3" />
        这个话题感觉非常实用。对，那我们就直接开始，怎么能够提升自己的商业思维？
        <prosody pitch="high" rate="slow">
            <emphasis level="moderate">怎么能够提升自己的商业思维？</emphasis>
        </prosody>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <prosody rate="medium">
            第一个问题就是咱们先聊一聊这个作者他自己是怎么进入到互联网行业的，然后又是怎么开始专注在商业化这个领域的。
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        他这个其实挺有意思的。
        <break time="500ms"/>
        他就是说他大二的时候听了一个学长的分享。那个学长就是拿到了互联网大厂的offer，然后年薪是十多万。
        <emphasis level="strong">他当时就是一下子被这个数字吸引了。</emphasis>
        <prosody rate="fast" volume="loud">
            因为当时正常的就业的月薪也就是5000左右，他就觉得这个差距。
        </prosody>
        所以他就从此开始想要进入这个领域，他就开始自学互联网产品经理的知识。他14年毕业之后就加入了腾讯。
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <prosody pitch="low" rate="slow">
            看来这个学长的这个分享真的改变了他的人生轨迹。
        </prosody>
        <break time="1.0s"/>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        是啊，<break time="300ms"/>
        然后他后来就是说他一开始是做用户沪产品的。但是他觉得用户产品就是体验的这个东西太不确定了。他也不是很认同，说一定要做到极致。他觉得好的体验是在一定的成本的框架里面去做的对，后来他就是一个偶然的机会接触到了商业化的业务。
        <audio src="https://example.com/transition-sound.mp3" />
        他就发现这个东西他特别感兴趣，就是你要去思考这个产品如何去赚钱。
        <emphasis level="strong">
            <prosody rate="fast" volume="loud">
                他就是一下子就被这个广告的计费模式的这种逻辑性给吸引住了。
            </prosody>
        </emphasis>
        对他就觉得这个东西相比用户产品而言，它的策略更可以量化，数据更可以衡量。对它就从此开始了它的商业化的深耕。它也负责过很多的业务，包括什么联盟广告，应用商店的广告，游戏联运等等非常多的业务。他也有做过百亿级的产品，也有做过失败的项目。
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <break time="300ms"/>
        但是他就是在这个过程当中积累了非常丰富的经验。为什么他会想要写这个商业思维的笔记，然后开这个专栏呢？
        <prosody pitch="high" rate="slow">
            <emphasis level="moderate">为什么他会想要写这个商业思维的笔记，然后开这个专栏呢？</emphasis>
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        因为他觉得他自己通过这么多年的工作，他积累了很多关于不同的产品的商业模式的一些知识。然后也对于这个互联网的灰尘有一些了解。最重要的是它锻炼出了一个用商业思维去剖析业务的一个能力。
        <emphasis level="strong">
            <prosody rate="medium">
                他觉得这个东西是非常重要的，尤其是在互联网这个江湖里面混。你如果没有这个商业思维的话，就相当于鱼在水里面不知道水是什么。
            </prosody>
        </emphasis>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <prosody pitch="high" rate="fast" volume="loud">
            看来这个商业思维确实是一个很厉害的东西。
        </prosody>
        <break time="1.0s"/>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        对，<break time="200ms"/>
        而且他就是说虽然现在在互联网的发展速度慢下来了，甚至在裁员，但是他觉得这反而是一个大家去学习这一些基础的原则，然后去了解这个行业的演进模式的一个好机会。
        <prosody rate="medium" volume="medium">
            因为他觉得未来的新的浪潮一定还是会在互联网的基础上面去诞生的。另外一个就是他也希望能够总结出一些比较简洁的框架，可以帮助新人或者说帮助一些想要转型的人，能够更快的去理解商业的逻辑，去抓住一些业务的破局点。所以他就会通过一些案例的剖析，来把这些东西分享给大家。
        </prosody>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        这个笔记它是通过一个什么样的结构来帮大家搭建这个商业思维的体系呢？
        <prosody pitch="high" rate="slow">
            <emphasis level="moderate">这个笔记它是通过一个什么样的结构来帮大家搭建这个商业思维的体系呢？</emphasis>
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        它是分成了三大模块儿，然后七个章节。
        <prosody rate="slow" pitch="medium">
            三大模块就是从基础的概念到商业的思维，再到最后的这个业务的操盘，它是一步一步递进的。每个部分其实都是有很明确的目的的。
        </prosody>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <break time="300ms"/>
        听起来很系统。
        <break time="0.5s"/>
        那每个部分具体都讲些什么呢？
        <prosody pitch="high" rate="slow">
            <emphasis level="moderate">那每个部分具体都讲些什么呢？</emphasis>
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        首先第一个模块就是基础商业化的基础知识。然后这里面它会通过一些真实的行业案例去教大家如何去创造价值，如何用一些工具去做商业的分析。
        <prosody rate="medium">
            第二个模块就是商业的升维那。这里面就是会通过一些复杂的商业案例去帮助大家理解这个不确定的商业环境。同时教大家用终局的思维去找到一些创新的突破点。
        </prosody>
        <emphasis level="moderate">
            第三个模块就是技巧的呈现，这里面就是会教大家一些在业务操盘的过程当中非常实用的一些工具。比如说如何去做行业的分析，如何去有效的呈现你的结论，以及如何去把控项目的节奏。
        </emphasis>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        我们在学习这个商业思维的笔记的过程当中，有哪些建议可以给大家。
        <break time="300ms"/>
        然后同时作者还有没有什么特别想要跟读者互动的地方？
        <prosody pitch="high" rate="slow">
            <emphasis level="moderate">然后同时作者还有没有什么特别想要跟读者互动的地方？</emphasis>
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        他其实建议就是大家不要有压力，然后把它当成一个故事来读。
        <prosody rate="slow" pitch="low">
            同时他是希望大家能够用一种批判性的思维去看。
        </prosody>
        <emphasis level="moderate">
            因为他觉得商业这个东西本身就是非常不确定的，所以他希望大家能够提出自己的疑问，包括给他一些反馈，这样的话可以一起共同进步。
        </emphasis>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <prosody pitch="medium" rate="slow">
            看来他是希望大家能够更主动的去想吸收这些知识。
        </prosody>
        <break time="1.0s"/>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        对，没错。<break time="200ms"/>
        而且他还说商业思维这个东西是一个长期主义的事情，不光是要在课堂里面学，你要在日常生活当中，用你的好奇心去挖掘一些案例，然后用你学到的东西去解构它，通过大量的练习形成你自己的本能的反应。
        <emphasis level="strong">
            最后他就是说非常希望能够听到大家的想法。比如说你对这个课程有什么期待，或者说你有什么问题，或者说你有什么商业的故事也可以跟他分享，就是有来有往的这种。
        </emphasis>
    </voice>
    
    <voice name="zh-CN-XiaoxiaoNeural">
        <audio src="https://example.com/transition-sound.mp3" />
        <prosody rate="medium">
            今天我们聊了这么多关于为什么互联网行业的朋友需要商业思维。然后包括这个作者他自己是怎么从一个非常偶然的机会进入到这个领域，一直走到商业化的这个比较深入的地方。以及他是怎么把这些东西总结出来分享给大家的。
        </prosody>
    </voice>
    
    <voice name="zh-CN-YunyangNeural">
        <prosody rate="medium" pitch="medium" volume="medium">
            Ok了，以上就是这期播客的全部内容了，然后咱们下期再见吧，拜拜。
        </prosody>
        <audio src="https://example.com/ending-sound.mp3" />
    </voice>
</speak> 
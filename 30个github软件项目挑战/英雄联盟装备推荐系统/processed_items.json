{"items": [{"itemId": "1001", "name": "鞋子", "price": "300", "total": "300", "description": "<mainText><stats><attention>25</attention>移动速度</stats><br><br></mainText>", "item_desc": "25移动速度", "into": ["3005", "3006", "3009", "3010", "3020", "3047", "3111", "3117", "3158"], "from": [], "types": ["Boots"], "sell": "210"}, {"itemId": "1004", "name": "仙女护符", "price": "200", "total": "200", "description": "<mainText><stats><attention>50%</attention>基础法力回复</stats><br><br></mainText>", "item_desc": "50%基础法力恢复", "into": ["3012", "3114", "4642"], "from": [], "types": ["ManaRegen"], "sell": "140"}, {"itemId": "1006", "name": "治疗宝珠", "price": "300", "total": "300", "description": "<mainText><stats><attention>100%</attention>基础生命回复</stats><br><br></mainText>", "item_desc": "100%基础生命恢复", "into": ["3109", "3211", "3801", "323109"], "from": [], "types": ["HealthRegen"], "sell": "120"}, {"itemId": "1011", "name": "巨人腰带", "price": "500", "total": "900", "description": "<mainText><stats><attention>350</attention>生命值</stats><br><br></mainText>", "item_desc": "350生命值", "into": ["2502", "3083", "3084", "3116", "3119", "3143", "3748", "4637", "6609", "6665", "6667", "8001", "323119"], "from": ["1028"], "types": ["Health"], "sell": "630"}, {"itemId": "1018", "name": "灵巧披风", "price": "600", "total": "600", "description": "<mainText><stats><attention>15%</attention>暴击几率</stats><br><br></mainText>", "item_desc": "15%暴击几率", "into": ["3031", "3033", "3086", "3095", "3508", "6670", "6676"], "from": [], "types": ["CriticalStrike"], "sell": "420"}, {"itemId": "1026", "name": "爆裂魔杖", "price": "850", "total": "850", "description": "<mainText><stats><attention>45</attention>法术强度</stats><br><br></mainText>", "item_desc": "45法术强度", "into": ["3100", "3115", "3116", "3118", "3135", "3165", "4628", "4637", "6621", "6657", "326621", "326657"], "from": [], "types": ["SpellDamage"], "sell": "595"}, {"itemId": "1027", "name": "蓝水晶", "price": "300", "total": "300", "description": "<mainText><stats><attention> 300</attention>法力</stats><br><br></mainText>", "item_desc": "300法力", "into": ["3024", "3802", "3803"], "from": [], "types": ["<PERSON><PERSON>"], "sell": "210"}, {"itemId": "1028", "name": "红水晶", "price": "400", "total": "400", "description": "<mainText><stats><attention>150</attention>生命值</stats><br><br></mainText>", "item_desc": "150生命值", "into": ["1011", "2021", "3012", "3023", "3044", "3053", "3066", "3067", "3075", "3147", "3152", "3161", "3211", "3742", "3801", "3803", "4401", "4635", "6035", "6610", "6660", "6662", "323050", "323075", "323107", "323222", "326617"], "from": [], "types": ["Health"], "sell": "280"}, {"itemId": "1029", "name": "布甲", "price": "300", "total": "300", "description": "<mainText><stats><attention>15</attention>护甲</stats><br><br></mainText>", "item_desc": "15护甲", "into": ["1031", "2019", "2420", "2421", "3023", "3024", "3047", "3050", "3076", "3082", "3105", "3190", "3193", "323190"], "from": [], "types": ["Armor"], "sell": "210"}, {"itemId": "1031", "name": "锁子甲", "price": "500", "total": "800", "description": "<mainText><stats><attention>40</attention>护甲</stats><br><br></mainText>", "item_desc": "40护甲", "into": ["3002", "3068", "3075", "3109", "3742", "6662", "6665", "323002", "323075", "323109"], "from": ["1029"], "types": ["Armor"], "sell": "560"}, {"itemId": "1033", "name": "抗魔斗篷", "price": "400", "total": "400", "description": "<mainText><stats><attention>20</attention>魔法抗性</stats><br><br></mainText>", "item_desc": "$400\n20魔法抗性", "into": ["1057", "3001", "3050", "3105", "3111", "3140", "3155", "3190", "3193", "3211", "4632", "323190"], "from": [], "types": ["SpellBlock"], "sell": "280"}, {"itemId": "1036", "name": "长剑", "price": "350", "total": "350", "description": "<mainText><stats><attention>10</attention>攻击力</stats><br><br></mainText>", "item_desc": "10攻击力", "into": ["1053", "2015", "2019", "2021", "3004", "3032", "3035", "3044", "3051", "3077", "3123", "3133", "3134", "3142", "3155", "4003", "6670", "6671", "6690", "6692", "6699", "6701", "323004"], "from": [], "types": ["Damage", "Lane"], "sell": "245"}, {"itemId": "1037", "name": "十字镐", "price": "875", "total": "875", "description": "<mainText><stats><attention>25</attention>攻击力</stats><br><br></mainText>", "item_desc": "25攻击力", "into": ["2020", "3031", "3039", "3053", "3071", "3072", "3087", "3124", "3139", "3153", "3161", "3181", "3508", "6029", "6035", "6333", "6673", "6676", "6692", "6695", "6701"], "from": [], "types": ["Damage"], "sell": "613"}, {"itemId": "1038", "name": "暴风之剑", "price": "1300", "total": "1300", "description": "<mainText><stats><attention>40</attention>攻击力</stats><br><br></mainText>", "item_desc": "40攻击力", "into": ["3026", "3031", "3032", "3072", "3146", "4403", "6671"], "from": [], "types": ["Damage"], "sell": "910"}, {"itemId": "1040", "name": "黑曜石锋刃", "price": "350", "total": "350", "description": "<mainText><stats></stats><br><br> <passive>8%</passive>对野怪的全能吸血<br><li><passive>烙印：</passive>对野怪造成伤害时会灼烧它们，在5秒里持续造成共<magicDamage>0魔法伤害</magicDamage>。<li><passive>自动之途：</passive>在使用5次【惩戒】后，会将这件装备<status>吞噬</status>，并将你的【惩戒】升级为【攻击惩戒】，使其对野怪的伤害提升至<trueDamage>900</trueDamage>。<li><passive>狩猎人：</passive>击杀大型野怪会提供额外经验值。<li><passive>取食于野：</passive>在野区或河道时，每秒至多回复<scaleMana>0法力值</scaleMana>。(基于已损失法力值)<br><br><rules><status>吞噬</status>这件装备时会永久提供它的所有效果并提升【惩戒】对野怪的伤害。如果你从小兵处获得的金币多于你从野怪处获得的金币，那么来自小兵的金币和经验将被大幅降低。群体攻击的治疗效果不会降低。 如果落后于对局内平均英雄等级且等级差达到2级，那么击杀野怪会提供额外经验值。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Jungle", "LifeSteal", "SpellVamp"], "sell": "140"}, {"itemId": "1042", "name": "短剑", "price": "250", "total": "250", "description": "<mainText><stats><attention>10%</attention>攻击速度</stats><br><br></mainText>", "item_desc": "10%攻击速度", "into": ["1043", "3006", "3046", "3051", "3073", "3086", "3131", "3144", "6631", "6675", "6677"], "from": [], "types": ["AttackSpeed"], "sell": "175"}, {"itemId": "1043", "name": "反曲之弓", "price": "450", "total": "700", "description": "<mainText><stats><attention>15%</attention>攻击速度</stats><br><br><passive>叮刺</passive><br>攻击附带<physicalDamage>15额外物理伤害</physicalDamage> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "15%攻击速度 \n叮刺\n攻击附带15额外物理伤害 攻击特效", "into": ["3091", "3115", "3124", "3153", "3302", "6672"], "from": ["1042"], "types": ["AttackSpeed", "OnHit"], "sell": "490"}, {"itemId": "1052", "name": "增幅典籍", "price": "400", "total": "400", "description": "<mainText><stats><attention>20</attention>法术强度</stats><br><br></mainText>", "item_desc": "20法术强度", "into": ["2420", "2421", "2508", "3108", "3113", "3116", "3124", "3145", "3147", "3802", "3916", "4630", "4632", "4635", "4637", "4642", "4644", "6656", "323504", "324005", "326616"], "from": [], "types": ["SpellDamage"], "sell": "280"}, {"itemId": "1053", "name": "吸血鬼节杖", "price": "550", "total": "900", "description": "<mainText><stats><attention>15</attention>攻击力<br><attention>7%</attention>生命偷取</stats><br><br></mainText>", "item_desc": "15攻击力\n7%生命偷取", "into": ["3072", "3074", "3139", "3153", "4403"], "from": ["1036"], "types": ["Damage", "LifeSteal"], "sell": "630"}, {"itemId": "1054", "name": "多兰之盾", "price": "450", "total": "450", "description": "<mainText><stats><attention>110</attention>生命值</stats><br><br><passive>耐久专注</passive><br>每5秒回复<healing>4生命值</healing>。<br>在承受来自英雄的伤害后，在8秒里回复<healing>生命值</healing>。<br><passive>帮助之手</passive><br>攻击对小兵造成<physicalDamage>5额外物理伤害</physicalDamage>。</mainText>", "item_desc": "110生命值 \n耐久专注\n【近战】每5秒回复4生命值。在承受来自英雄的伤害后，在8秒里至多回复45生命值。\n【远程】\n帮助之手\n攻击会对小兵造成额外的5物理伤害。\n受到伤害后的治疗数额是基于你的已损失生命值。\n受到群体伤害或周期型伤害时，治疗的效能为66%。", "into": [], "from": [], "types": ["Health", "HealthRegen", "Lane"], "sell": "180"}, {"itemId": "1055", "name": "多兰之刃", "price": "450", "total": "450", "description": "<mainText><stats><attention>10</attention>攻击力<br><attention>80</attention>生命值<br><attention>3%</attention>生命偷取</stats><br><br></mainText>", "item_desc": "10攻击力\n80生命值\n3%生命偷取", "into": [], "from": [], "types": ["Damage", "Health", "Lane", "SpellVamp"], "sell": "180"}, {"itemId": "1056", "name": "多兰之戒", "price": "400", "total": "400", "description": "<mainText><stats><attention>18</attention>法术强度<br><attention>90</attention>生命值</stats><br><br><passive>回复力</passive><br>每秒回复<scaleMana>1.25法力值</scaleMana>。如果你不能获得法力值，则转而回复<scaleHealth>0.55生命值</scaleHealth>。<br><br><passive>帮助之手</passive><br>攻击对小兵造成<physicalDamage>5额外物理伤害</physicalDamage>。</mainText>", "item_desc": "18法术强度\n90生命值 \n回复力\n每秒回复1.25法力值。如果你不能获得法力值，则转而回复0.55生命值。\n帮助之手\n攻击会对小兵造成额外的5物理伤害。", "into": [], "from": [], "types": ["Health", "Lane", "ManaRegen", "SpellDamage"], "sell": "160"}, {"itemId": "1057", "name": "负极斗篷", "price": "450", "total": "850", "description": "<mainText><stats><attention>45</attention>魔法抗性</stats><br><br></mainText>", "item_desc": "45魔法抗性", "into": ["2504", "3091", "4401", "6665", "8020", "328020"], "from": ["1033"], "types": ["SpellBlock"], "sell": "595"}, {"itemId": "1058", "name": "无用大棒", "price": "1200", "total": "1200", "description": "<mainText><stats><attention>65</attention>法术强度</stats><br><br></mainText>", "item_desc": "65法术强度", "into": ["3089", "3102", "3128", "3146", "3157", "4403", "4645"], "from": [], "types": ["SpellDamage"], "sell": "840"}, {"itemId": "1082", "name": "黑暗封印", "price": "350", "total": "350", "description": "<mainText><stats><attention>15</attention>法术强度<br><attention>50</attention>生命值</stats><br><br><passive>荣耀</passive><br><keyword>参与击杀</keyword>后提供<passive>荣耀</passive>层数，至多至10层。阵亡时损失5层<passive>荣耀</passive>。<br>每层<passive>荣耀</passive>提供<scaleAP>4法术强度</scaleAP>。</mainText>", "item_desc": "15法术强度\n50生命值 \n荣耀\n参与击杀后提供荣耀层数，至多至10层。阵亡时损失5层荣耀。\n每层荣耀提供4法术强度。\n击杀提供2层荣耀，助攻提供1层。\n参与击杀:获得击杀数和助攻数都被算作一次参与击杀。", "into": ["3041"], "from": [], "types": ["Health", "Lane", "SpellDamage"], "sell": "140"}, {"itemId": "1083", "name": "萃取", "price": "450", "total": "450", "description": "<mainText><stats><attention>7</attention>攻击力</stats><br><br><passive>收割</passive><br>回复<healing>3生命值</healing><OnHit>攻击特效</OnHit>。<br>击杀小兵时提供<gold>1金币</gold>，至多至<gold>100</gold>。达到上限时提供额外的<gold>350金币</gold>。<br></mainText>", "item_desc": "7攻击力 \n收割\n每次命中回复3生命值。\n击杀1个线上小兵时提供1额外金币，至多至100。达到上限时提供额外的350金币。", "into": [], "from": [], "types": ["Damage", "Lane", "OnHit"], "sell": "180"}, {"itemId": "1101", "name": "焰爪猫幼崽", "price": "450", "total": "450", "description": "<mainText><stats></stats><br><br><passive>灵兽同伴</passive><br>召唤一个焰爪猫来协助你对抗野怪。<br><br><passive>焰爪猫的斩击</passive><br>这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的<spellName>惩戒</spellName>。在完全成长时，它会周期性地为你的下一个攻击或技能进行注魔，以对敌人造成灼烧和<status>减速</status>效果。击杀大型野怪后会让这个效果立刻就绪。</mainText>", "item_desc": "需要召唤师技能惩戒\n灵兽同伴\n召唤一个焰爪猫，在你对抗野怪时每秒造成(【随等级提升】【攻击力】【法术强度】【生命值】【护甲】【魔抗】)伤害并每秒治疗你(14-37【随等级提升】)生命值。\n\n焰爪猫的斩击\n这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的惩戒。在完全成长时，它会周期\n件地为你的下一个攻击或技能进行注魔，以对敌人造成灼烧(在4秒里持续造成4%最大\n生命值的真实伤害)和在2秒里持续衰减的30%减速。击杀大型野怪后会让这个效果立\n刻就绪。\n- 灵兽同伴可被投喂至多40个小点心。你在击杀一个大型野怪时会对它进行一次投喂，如果你有额外的小点心则会进行两次投喂。你会周期性地获得额外的小点心来投喂你的灵兽同伴，并且这些小点心还会提供额外金币。 \n\n- 20个小点心:你的惩戒造成900伤害。它可对敌方英雄施放以对其造成削减过的伤害以及减速效果。\n\n- 40个小点心:你的惩戒对附近野怪造成1200伤害。\n\n对史诗级野怪的灵兽同伴伤害上限为18(【攻击力】【法术强度】【生命值】【护甲】【魔抗】)\n每击杀一个大型野怪获得额外的80经验值，并且会在首次击杀大型野怪时获得150额外经验值。", "into": [], "from": [], "types": ["Jungle"], "sell": "0"}, {"itemId": "1102", "name": "风行狐幼体", "price": "450", "total": "450", "description": "<mainText><stats></stats><br><br><passive>灵兽同伴</passive><br>召唤一个风行狐来协助你对抗野怪。<br><br><passive>风行狐的轻灵</passive><br>这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的<spellName>惩戒</spellName>。在完全成长时，它会在进入草丛的同时提供<speed>移动速度</speed>，移动速度的数额会在击杀大型野怪后提升。</mainText>", "item_desc": "需要召唤师技能惩戒\n\n灵兽同伴\n召唤一个风行狐，在你对抗野怪时每秒造成(【随等级提升】【攻击力】【法术强度】【生命值】【护甲】【魔抗】)伤害并每秒治疗你(14-37【随等级提升】)生命值。\n\n风行狐的轻灵\n这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的惩戒。在完全成长时，它在进入草从的同时会提供在2秒里持续衰减的30%移动速度。击杀大型野怪时会使这个移速提升至45%。\n\n- 灵兽同伴可被投喂至多40个小点心。你在击杀一个大型野怪时会对它进行一次投喂，如果你有额外的小点心则会进行两次投喂。你会周期性地获得额外的小点心来投喂你的灵兽同伴，并且这些小点心还会提供额外金币。\n- 20个小点心:你的惩戒造成900伤害。它可对敌方英雄施放以对其造成削减过的伤害以及减速效果。\n- 40个小点心:你的惩戒对附近野怪造成1200伤害。\n\n对史诗级野怪的灵兽同伴伤害上限为18(【攻击力】【法术强度】【生命值】【护甲】【魔抗】)。\n每击杀一个大型野怪获得额外的80经验值，并且会在首次击杀大型野怪时获得150额外经验值。", "into": [], "from": [], "types": ["Jungle"], "sell": "0"}, {"itemId": "1103", "name": "踏苔蜥幼苗", "price": "450", "total": "450", "description": "<mainText><stats></stats><br><br><passive>灵兽同伴</passive><br>召唤一个踏苔蜥来协助你对抗野怪。<br><br><passive>踏苔蜥的勇气</passive><br>这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的<spellName>惩戒</spellName>。在完全成长时，它会提供一个<shield>护盾</shield>，这个护盾可在击杀大型野怪或脱离战斗后重新生成。</mainText>", "item_desc": "需要召唤师技能惩戒\n\n灵兽同伴\n召唤一个踏苔蜥，在你对抗野怪时每秒造成(【随等级提升】【攻击力】【法术强度】【生命值】【护甲】【魔抗】)伤害并每秒治疗你(14-37【随等级提升】)生命值。\n\n踏苔蜥的勇气\n这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的惩戒。在完全成长时，它会提供(180-300【随等级提升】)护盾值，这个护盾可在击杀大型野怪或脱离战斗10秒后重新生成。\n\n- 灵兽同伴可被投喂至多40个小点心。你在击杀一个大型野怪时会对它进行一次投喂，如果你有额外的小点心则会进行两次投喂。你会周期性地获得额外的小点心来投喂你的灵兽同伴，并且这些小点心还会提供额外金币，\n- 20个小点心:你的惩戒造成900伤害。它可对敌方英雄施放以对其造成削减过的伤害以及减速效果。\n- 40个小点心:你的惩戒对附近野怪造成1200伤害。\n对史诗级野怪的灵兽同伴伤害上限为18(【攻击力】【法术强度】【生命值】【护甲】【魔抗】)。\n每击杀一个大型野怪获得额外的80经验值，并且会在首次击杀大型野怪时获得150额外经验值。", "into": [], "from": [], "types": ["Jungle"], "sell": "0"}, {"itemId": "1105", "name": "踏苔蜥幼苗", "price": "450", "total": "450", "description": "<mainText><stats></stats><br><br><passive>灵兽同伴</passive><br>召唤一个踏苔蜥来协助你对抗野怪。<br><br><passive>踏苔蜥的勇气</passive><br>这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的<spellName>惩戒</spellName>。在完全成长时，它会提供一个<shield>护盾</shield>，这个护盾可在击杀大型野怪或脱离战斗后重新生成。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Jungle"], "sell": "180"}, {"itemId": "1106", "name": "风行狐幼体", "price": "450", "total": "450", "description": "<mainText><stats></stats><br><br><passive>灵兽同伴</passive><br>召唤一个风行狐来协助你对抗野怪。<br><br><passive>风行狐的轻灵</passive><br>这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的<spellName>惩戒</spellName>。在完全成长时，它会在进入草丛的同时提供<speed>移动速度</speed>，移动速度的数额会在击杀大型野怪后提升。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Jungle"], "sell": "180"}, {"itemId": "1107", "name": "焰爪猫幼崽", "price": "450", "total": "450", "description": "<mainText><stats></stats><br><br><passive>灵兽同伴</passive><br>召唤一个焰爪猫来协助你对抗野怪。<br><br><passive>焰爪猫的斩击</passive><br>这个灵兽同伴会随着你狩猎野怪而成长，从而强化你的<spellName>惩戒</spellName>。在完全成长时，它会周期性地为你的下一个攻击或技能进行注魔，以对敌人造成灼烧和<status>减速</status>效果。击杀大型野怪后会让这个效果立刻就绪。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Jungle"], "sell": "180"}, {"itemId": "2003", "name": "生命药水", "price": "50", "total": "50", "description": "<mainText><stats></stats><br><br><active>消耗</active><br>在15秒里持续回复共<healing>120生命值</healing>。</mainText>", "item_desc": "在15秒里持续回复共120生命值\n你最多可携带5瓶生命药水。", "into": [], "from": [], "types": ["Consumable", "HealthRegen", "Jungle", "Lane"], "sell": "20"}, {"itemId": "2019", "name": "钢铁印章", "price": "150", "total": "1100", "description": "<mainText><stats><attention>15</attention>攻击力<br><attention>30</attention>护甲</stats><br><br></mainText>", "item_desc": "15攻击力\n30护甲", "into": ["3026", "6333", "6700"], "from": ["1029", "1029", "1036"], "types": ["Armor", "Damage"], "sell": "770"}, {"itemId": "2020", "name": "残暴之力", "price": "212", "total": "1337", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>10</attention>技能急速<br><attention>5</attention>穿甲</stats><br><br></mainText>", "item_desc": "25攻击力\n10技能急速\n5穿甲", "into": ["6696", "6698", "6699"], "from": ["1037", "2022"], "types": ["ArmorPenetration", "CooldownReduction", "Damage"], "sell": "936"}, {"itemId": "2021", "name": "掘道钻头", "price": "400", "total": "1150", "description": "<mainText><stats><attention>15</attention>攻击力<br><attention>250</attention>生命值</stats><br><br></mainText>", "item_desc": "15攻击力\n250生命值", "into": ["2501", "3053", "3073", "3161", "3181", "3748", "3814", "6610"], "from": ["1028", "1036"], "types": ["Damage", "Health"], "sell": "805"}, {"itemId": "2022", "name": "荧尘", "price": "250", "total": "250", "description": "<mainText><stats><attention>5</attention>技能急速</stats><br><br></mainText>", "item_desc": "5技能急速", "into": ["2020", "3024", "3057", "3067", "3108", "3133", "3158", "3802", "4642", "6660"], "from": [], "types": ["CooldownReduction"], "sell": "175"}, {"itemId": "2031", "name": "复用型药水", "price": "150", "total": "150", "description": "<mainText><stats></stats><br><br><active>主动</active> (2层)<br>在12秒里持续回复共<healing>100生命值</healing>。<br>在访问商店时重新补满层数。</mainText>", "item_desc": "主动(2层)\n在12秒里持续回复共100生命值。\n在访问商店时重新补满层数。", "into": ["2033"], "from": [], "types": ["Active", "Consumable", "HealthRegen", "Jungle", "Lane"], "sell": "60"}, {"itemId": "2049", "name": "守护者护符", "price": "500", "total": "500", "description": "<mainText><stats><attention> 15%</attention>治疗和护盾强度<br><attention>20</attention>法术强度<br><attention>20</attention>技能急速</stats><br><br><passive>复原力</passive><br>每5秒回复<scaleMana>10法力值</scaleMana>。如果你不能获得法力值，则转而回复<healing>3生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Lane", "ManaRegen", "SpellDamage"], "sell": "250"}, {"itemId": "2050", "name": "守护者法衣", "price": "500", "total": "500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>35</attention>法术强度<br><attention>15</attention>技能急速</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "Lane", "SpellDamage"], "sell": "250"}, {"itemId": "2051", "name": "守护者号角", "price": "950", "total": "950", "description": "<mainText><stats><attention>150</attention>生命值</stats><br><br><br><li><passive>复原力：</passive>每5秒回复<healing>20生命值</healing>。<li><passive>不屈不挠：</passive>格挡来自英雄的攻击和技能的15伤害(对抗持续伤害技能时的效能为25%)。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "HealthRegen", "Lane"], "sell": "665"}, {"itemId": "2055", "name": "控制守卫", "price": "75", "total": "75", "description": "<mainText><stats></stats><br><br><active>消耗</active><br>放置一个控制守卫，它可以提供视野并显形敌方的侦察守卫、陷阱和<keywordStealth>伪装</keywordStealth>状态的敌人。</mainText>", "item_desc": "消耗\n放置一个控制守卫，它可以提供视野并显形敌方的侦察守卫、陷阱和伪装状态的敌人。\n你最多可以携带2个控制守卫。\n被显形的侦察守卫会在期间内失效。", "into": [], "from": [], "types": ["Consumable", "Lane", "Stealth", "Vision"], "sell": "30"}, {"itemId": "2065", "name": "舒瑞娅的战歌", "price": "400", "total": "2200", "description": "<mainText><stats><attention>50</attention>法术强度<br><attention>15</attention>技能急速<br><attention>4%</attention>移动速度<br><attention>125%</attention>基础法力回复</stats><br><br> <active>鼓舞演讲</active><br>为附近友军们提供持续4秒的<speed>30%移动速度</speed>。</mainText>", "item_desc": "50法术强度\n15技能急速\n4%移动速度\n125%基础法力回复 \n鼓舞演讲(75秒)\n为附近友军们提供持续4秒的30%移动速度。", "into": [], "from": ["3113", "4642"], "types": ["AbilityHaste", "Active", "CooldownReduction", "ManaRegen", "NonbootsMovement", "SpellDamage"], "sell": "1540"}, {"itemId": "2138", "name": "钢铁合剂", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><active>消耗</active><br>获得<scaleHealth>300生命值</scaleHealth>，25%韧性，并提升体型，持续3分钟。在合剂生效期间，你会在身后留下一条道路，友方英雄在这条道路上会<speed>提升15%移动速度</speed>。</mainText>", "item_desc": "需要达到等级9\n消耗\n获得300生命值，25%韧性，并提升体型，持续3分钟。在合剂生效期间，你会在身后留下一条道路，友方英雄在这条道路上会提升15%移动速度。\n饮用一瓶不同的合剂将替换掉现有合剂的效果。", "into": [], "from": [], "types": ["Consumable", "Health", "NonbootsMovement", "Tenacity"], "sell": "200"}, {"itemId": "2139", "name": "巫术合剂", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><active>消耗</active><br>获得<scaleAP>50法术强度</scaleAP>和<scaleMana>15%法力回复</scaleMana>，持续3分钟。在合剂生效期间，对英雄或防御塔造成伤害时会造成<trueDamage>25额外真实伤害</trueDamage> (对英雄的冷却为5秒)。</mainText>", "item_desc": "需要达到等级9。\n\n消耗\n获得50法术强度和15%法力回复，持续3分钟。在合剂生效期间，对英雄或防御塔造成伤害时会造成25额外真实伤害(对英雄的冷却为5秒)。\n\n饮用一瓶不同的合剂将替换掉现有合剂的效果。", "into": [], "from": [], "types": ["Consumable", "ManaRegen", "SpellDamage"], "sell": "200"}, {"itemId": "2140", "name": "愤怒合剂", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><active>消耗</active><br>获得<scaleAD>30攻击力</scaleAD>和<lifeSteal>12%物理吸血</lifeSteal>(对英雄)，持续3分钟。</mainText>", "item_desc": "需要达到等级9。\n\n消耗\n获得30攻击力和12%物理吸血(对英雄)，持续3分钟。\n\n饮用一瓶不同的合剂将替换掉现有合剂的效果。", "into": [], "from": [], "types": ["Consumable", "Damage", "LifeSteal", "SpellVamp"], "sell": "200"}, {"itemId": "2141", "name": "帽子饮品", "price": "300", "total": "300", "description": "<mainText><stats></stats><br><br><rules>帮助你出人头地。</rules><active>主动 - 消耗：</active>这个饮品什么也不做。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "Damage"], "sell": "300"}, {"itemId": "2142", "name": "威能饮品", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><br><br><rules>饮品不会自我叠加，但你可以同时激活多个不同的饮品。</rules><active>主动 - 消耗：</active>在战斗开始时自动激活。饮用以在下一个回合获得 <scaleAP>30 + 10%额外法术强度</scaleAP>或<scaleAD>18 + 10%额外攻击力</scaleAD>。<br><br><flavorText>原料是100%真樱桃。警告：可能会导致饮用者造成成吨的伤害。</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "SpellDamage"], "sell": "500"}, {"itemId": "2143", "name": "活力饮品", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><br><br><rules>饮品不会自我叠加，但你可以同时激活多个不同的饮品。</rules><active>主动 - 消耗：</active>在战斗开始时自动激活。饮用以在下一个回合获得<healing>300 + 10%生命值</healing>。<br><br><flavorText>事实证明，我们专门配制的蔬菜调制饮品可以硬化您的皮肤，甚至能抵御最强烈的攻击！</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "Health"], "sell": "500"}, {"itemId": "2144", "name": "急速饮品", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><br><br><rules>饮品不会自我叠加，但你可以同时激活多个不同的饮品。</rules><active>主动 - 消耗：</active>在战斗开始时自动激活。饮用以在下一个回合获得<speed>20 + 15%技能急速</speed> 。<br><br><flavorText>适合那些需要快速行动的人。用闪电制成。真正的闪电！</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "CooldownReduction"], "sell": "500"}, {"itemId": "2161", "name": "班德尔威能饮品", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br>饮用以永久获得<scaleAP>20 + 3%额外法术强度</scaleAP>或<scaleAD>12 + 3%额外攻击力</scaleAD>。以及一顶帽子！<br><br><flavorText>原料是100%真樱桃。警告：可能导致饮用者造成成吨的伤害。</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "SpellDamage"], "sell": "1000"}, {"itemId": "2162", "name": "班德尔活力饮品", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br>饮用以永久获得<healing>200 + 3%生命值</healing>。以及一顶帽子！<br><br><flavorText>事实证明，我们专门配制的蔬菜调制饮品可以硬化您的皮肤，甚至能抵御最强烈的攻击！</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "Health"], "sell": "1000"}, {"itemId": "2163", "name": "班德尔急速饮品", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br>饮用以永久获得<speed>10 + 5%技能急速</speed>。以及一顶帽子！<br><br><flavorText>适合那些需要快速行动的人。用闪电制成。真正的闪电！</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "CooldownReduction"], "sell": "1000"}, {"itemId": "2420", "name": "探索者的护臂", "price": "500", "total": "1600", "description": "<mainText><stats><attention>40</attention>法术强度<br><attention>25</attention>护甲</stats><br><br> <active>时间停止</active>(单次使用)<br>进入<keyword>凝滞</keyword>状态2.5秒。</mainText>", "item_desc": "40法术强度\n25护甲 \n\n时间停止(单次使用)\n进入凝滞状态2.5秒\n凝滞：变得免疫伤害且不可被选取。在此期间无法移动。", "into": ["3157"], "from": ["1029", "1052", "1052"], "types": ["Active", "Armor", "SpellDamage"], "sell": "640"}, {"itemId": "2421", "name": "碎裂的护臂", "price": "500", "total": "1600", "description": "<mainText><stats><attention>40</attention>法术强度<br><attention>25</attention>护甲</stats><br><br><passive>支离破碎的时间</passive><br>【护臂】目前是破碎状态，但仍然可以用于升级。<br><br><rules>在打破一个【护臂】后，商店主人就只会卖给你<rarityGeneric>碎裂的护臂</rarityGeneric>了。</rules><br><br></mainText>", "item_desc": "45法术强度\n25护甲\n\n支离破碎的时间\n【护臂】目前是破碎状态，但仍然可以用于升级。\n\n在打破一个【护臂】后，商店主人就只会卖给你碎裂的护臂了。\n破损无法修复……但魔法依然残存。", "into": ["3157"], "from": ["1029", "1052", "1052"], "types": [], "sell": "640"}, {"itemId": "2501", "name": "霸王血铠", "price": "1000", "total": "3300", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>550</attention>生命值</stats><br><br><passive>专横</passive><br>获得相当于你2.5%<healing>额外生命值</healing>的<scaleAD>攻击力</scaleAD>。<br><br><passive>报复</passive><br>获得基于你的百分比已损失生命值的12%<scaleAD>攻击力</scaleAD>提升。</mainText>", "item_desc": "30攻击力\n550生命值 \n\n专横\n获得相当于你2.5%额外生命值的攻击力。\n\n报复\n获得至多12%攻击力，基于你的已损失生命值。\n\n报复的最大加成会在低于30%生命值时达到。\n\n只有在生死之间，他才找到了找回场子的办法。", "into": [], "from": ["2021", "2021"], "types": ["Damage", "Health"], "sell": "2310"}, {"itemId": "2502", "name": "无终恨意", "price": "800", "total": "2800", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>25</attention>护甲<br><attention>25</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br><passive>苦楚</passive><br>在与英雄的战斗状态下，每4秒对附近的敌方英雄们造成<magicDamage>魔法伤害</magicDamage>，并治疗自身<healing>相当于250%已造成伤害的生命值</healing>。</mainText>", "item_desc": "400生命值\n25护甲\n25魔法抗性\n10技能急速 \n苦楚\n在与英雄的战斗状态下，每4秒对附近的敌方英雄们造成(3%【额外生命值】)魔法伤害，并治疗自身相当于250%已造成伤害的生命值。", "into": [], "from": ["1011", "3105"], "types": ["AbilityHaste", "Armor", "CooldownReduction", "Health", "SpellBlock"], "sell": "1960"}, {"itemId": "2503", "name": "黯炎火炬", "price": "700", "total": "2800", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention> 600</attention>法力<br><attention>20</attention>技能急速</stats><br><br><passive>邪焰</passive><br>伤害型技能造成<magicDamage>额外魔法伤害</magicDamage>，持续3秒。<br><br><passive>黯炎</passive><br>被你的<passive>邪焰</passive>影响的每个敌方英雄、史诗级野怪和大型野怪都会为你提供<scaleAP>4%法术强度</scaleAP>。</mainText>", "item_desc": "80法术强度\n600法力\n20技能急速\n\n邪焰\n伤害型技能造成每秒(20 +2%)额外魔法伤害，持续3秒。\n\n黯炎\n被你的邪焰影响的每个敌方英雄、史诗级野怪和大型野怪都会为你提供4%法术强度。\n\n邪焰对野怪造成额外的60魔法伤害\n\n\"黑雾需要一团程度与之相配的火焰。”", "into": [], "from": ["2508", "3802"], "types": ["AbilityHaste", "CooldownReduction", "<PERSON><PERSON>", "SpellDamage"], "sell": "1960"}, {"itemId": "2504", "name": "败魔", "price": "800", "total": "2900", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>80</attention>魔法抗性<br><attention>100%</attention>基础生命回复</stats><br><br><passive>法师之祸</passive><br>如果连续15秒没有受到魔法伤害，获得<shield>魔法护盾</shield>。 </mainText>", "item_desc": "400生命值\n80魔法抗性\n100%基础生命回复 \n\n法师之祸\n如果连续15秒没有受到魔法伤害，获得(15%【生命值】)魔法护盾。\n\n命运将凭借诡诈、汗水与钢铁来塑造。魔法再也不能违背这个准则了。”", "into": [], "from": ["1057", "3211"], "types": ["Health", "HealthRegen", "SpellBlock"], "sell": "2030"}, {"itemId": "2508", "name": "命定灰烬", "price": "500", "total": "900", "description": "<mainText><stats><attention>30</attention>法术强度</stats><br><br><passive>激燃</passive><br>伤害型技能在3秒里持续造成<magicDamage>15额外魔法伤害</magicDamage>。<br>对野怪造成额外的<magicDamage>45魔法伤害</magicDamage>。</mainText>", "item_desc": "30法术强度 \n\n激燃\n伤害型技能在3秒里持续造成15额外魔法伤害。\n对野怪造成额外的45魔法伤害。", "into": ["2503", "6653"], "from": ["1052"], "types": ["SpellDamage"], "sell": "630"}, {"itemId": "3002", "name": "引路者", "price": "800", "total": "2400", "description": "<mainText><stats><attention>250</attention>生命值<br><attention>40</attention>护甲<br><attention>4%</attention>移动速度</stats><br><br><passive>引路</passive><br>在移动时，积攒至多<speed>20额外移动速度</speed>。<br>在最大速度时：<li>生成一条路径来使你的友方英雄们加快<speed>移动速度，数额相当于你的15%移动速度</speed>。<li>如果你是近战英雄，你的下一次攻击还会对目标造成持续1秒的50%<keyword>减速</keyword>。<br><br></mainText>", "item_desc": "250生命值\n40护甲\n4%移动速度\n\n引路\n在移动时，获得20额外移动速度。\n在最大速度时：\n- 生成一条路径来使你的友方英雄们加快15%移动速度。数额相当于你的15%移动速度。\n- 如果你是【近战】近战英雄，你的下一次攻击还会对目标造成持续1秒的50%减速。\n\n前路由我闯。", "into": [], "from": ["1031", "3066"], "types": ["Armor", "Health", "NonbootsMovement"], "sell": "1680"}, {"itemId": "3003", "name": "大天使之杖", "price": "450", "total": "2900", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention> 600</attention>法力<br><attention>25</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得相当于<scaleMana>1%额外法力值</scaleMana>的法术强度。<br><br><passive>法力流</passive> (8秒，最大5层充能)<br>技能在命中时提供<scaleMana>5最大法力值</scaleMana>(对英雄时翻倍)。<br>在<scaleMana>360最大法力值</scaleMana>时转变为<rarityLegendary>炽天使之拥</rarityLegendary>。</mainText>", "item_desc": "70法术强度\n600法力\n25技能急速 \n\n敬畏\n获得相当于1%额外法力值的法术强度。\n\n法力流(8秒，最大5层充能)\n在技能命中时提供5最大法力值(命中英雄时翻倍)。\n在360最大法力值时转变为炽天使之拥。", "into": [], "from": ["3070", "3108", "3802"], "types": ["AbilityHaste", "<PERSON><PERSON>", "SpellDamage"], "sell": "2030"}, {"itemId": "3004", "name": "魔宗", "price": "1100", "total": "2900", "description": "<mainText><stats><attention>35</attention>攻击力<br><attention> 500</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得<scaleAD>0额外攻击力</scaleAD>。<br><br><passive>法力流</passive>  (8秒，最大4层)<br>普攻和技能在命中时会提供<scaleMana>3最大法力值</scaleMana>(对英雄时翻倍)。<br>在<scaleMana>360最大法力值</scaleMana>时转变为<rarityLegendary>魔切</rarityLegendary>。</mainText>", "item_desc": "35攻击力\n500法力\n15技能急速 \n\n敬畏\n获得(2%【法力值】)额外攻击力。\n\n法力流(8秒，最大4层)\n普攻和技能在命中时会提供3最大法力值(对英雄时翻倍)。\n在360最大法力值时转变为魔切。", "into": [], "from": ["1036", "3070", "3133"], "types": ["AbilityHaste", "CooldownReduction", "Damage", "<PERSON><PERSON>", "OnHit"], "sell": "2030"}, {"itemId": "3005", "name": "鬼蟹", "price": "700", "total": "1000", "description": "<mainText><stats><attention>45</attention>移动速度</stats><br><br> <active>墙体行走：</active>  (0秒)<br>获得持续6秒的穿墙行走能力。在墙体内时，获得125移动速度。施放一个技能或进行攻击将结束这个效果。</mainText>", "item_desc": "", "into": [], "from": ["1001"], "types": ["Boots"], "sell": "700"}, {"itemId": "3006", "name": "狂战士胫甲", "price": "300", "total": "1100", "description": "<mainText><stats><attention>25%</attention>攻击速度<br><attention>45</attention>移动速度</stats><br><br></mainText>", "item_desc": "25%攻击速度\n45移动速度", "into": ["3172"], "from": ["1001", "1042", "1042"], "types": ["AttackSpeed", "Boots"], "sell": "770"}, {"itemId": "3009", "name": "轻灵之靴", "price": "700", "total": "1000", "description": "<mainText><stats><attention>55</attention>移动速度</stats><br><br><passive>迅捷步</passive><br>将<keyword>减速</keyword>效果的效能降低25%。</mainText>", "item_desc": "55移动速度 \n\n迅捷步\n将减速效果的效能降低25%。", "into": ["3170"], "from": ["1001"], "types": ["Boots"], "sell": "700"}, {"itemId": "3010", "name": "共生鞋鱼", "price": "600", "total": "900", "description": "<mainText><stats><attention>40</attention>移动速度</stats><br><br><passive>灵犀</passive><br>非战斗状态下获得<speed>10移动速度</speed>。<br><br><passive>共生</passive><br>在移动150000码后，变形为<rarityLegendary>灵犀众魂</rarityLegendary>。</mainText>", "item_desc": "40移动速度 \n\n灵犀\n非战斗状态下获得10移动速度。\n\n共生\n在移动150000码后，变形为灵犀众魂\n'每走一步，你的脑海就会溜走一些东西……'", "into": ["3013"], "from": ["1001"], "types": ["Boots"], "sell": "630"}, {"itemId": "3020", "name": "法师之靴", "price": "800", "total": "1100", "description": "<mainText><stats><attention>12</attention>法术穿透<br><attention>45</attention>移动速度</stats><br><br></mainText>", "item_desc": "12法术穿透\n45移动速度", "into": ["3175"], "from": ["1001"], "types": ["Boots", "MagicPenetration"], "sell": "770"}, {"itemId": "3024", "name": "冰川圆盾", "price": "50", "total": "900", "description": "<mainText><stats><attention>25</attention>护甲<br><attention> 300</attention>法力<br><attention>10</attention>技能急速</stats><br><br></mainText>", "item_desc": "25护甲\n300法力\n10技能急速", "into": ["3110", "323110"], "from": ["1027", "1029", "2022"], "types": ["AbilityHaste", "Armor", "CooldownReduction", "<PERSON><PERSON>"], "sell": "630"}, {"itemId": "3026", "name": "守护天使", "price": "800", "total": "3200", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>45</attention>护甲</stats><br><br><passive>重生</passive><br>你的英雄在受到致命伤害时，会在<keyword>凝滞</keyword>4秒后原地复活，恢复<healing>50%基础生命值</healing>和<scaleMana>100%最大法力值</scaleMana>。</mainText>", "item_desc": "55攻击力\n45护甲 \n\n重生(300秒)\n你的英雄在受到致命伤害时，会在凝滞4秒后原地复活，恢复50%基础生命值和100%最大法力值。\n凝滞:变得免疫伤害且不可被选取。在此期间无法移动。", "into": [], "from": ["1038", "2019"], "types": ["Armor", "Damage"], "sell": "1280"}, {"itemId": "3031", "name": "无尽之刃", "price": "675", "total": "3450", "description": "<mainText><stats><attention>65</attention>攻击力<br><attention>25%</attention>暴击几率<br><attention>40%</attention>暴击伤害</stats><br><br></mainText>", "item_desc": "65攻击力\n25%暴击几率\n40%暴击伤害", "into": [], "from": ["1018", "1037", "1038"], "types": ["CriticalStrike", "Damage"], "sell": "2415"}, {"itemId": "3032", "name": "育恩塔尔荒野箭", "price": "750", "total": "3000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>35%</attention>攻击速度<br><attention>0%</attention>暴击几率</stats><br><br><passive>熟能生巧</passive><br>攻击时，永久获得暴击几率，至多至25%。<br><br><passive>疾风骤雨</passive> <br>攻击一个敌方英雄时，获得持续6秒的30%攻击速度(30秒冷却时间)。<br>攻击可使这个冷却时间缩短1秒，这个缩减值会在暴击时提升至2秒。</mainText>", "item_desc": "55攻击力\n35%攻击速度\n\n熟能生巧\n【近战】攻击时，永久获得+0.4%暴击几率，至多至25%。\n【远程】攻击时，永久获得+0.2%暴击几率，至多至25%。\n疾风骤雨(30秒)\n攻击一个敌方英雄时，获得持续6秒的30%攻击速度\n攻击可使这个冷却时间缩减1秒，这个缩减值会在暴击时提升至2秒\n\n传承给一个又一个女猎手，打败了一个又一个敌人。", "into": [], "from": ["1036", "1038", "3144"], "types": ["AttackSpeed", "CriticalStrike", "Damage"], "sell": "2100"}, {"itemId": "3033", "name": "凡性的提醒", "price": "450", "total": "3300", "description": "<mainText><stats><attention>35</attention>攻击力<br><attention>35%</attention>护甲穿透<br><attention>25%</attention>暴击几率</stats><br><br><passive>重伤</passive><br>对敌方英雄造成物理伤害时会施加<keyword>40%重伤</keyword>效果，持续3秒。</mainText>", "item_desc": "35攻击力\n35%护甲穿透\n25%暴击几率 \n\n重伤\n对敌方英雄造成物理伤害时会施加40%重伤效果，持续3秒。\n\n重伤:降低治疗效果和生命回复的效能。", "into": [], "from": ["1018", "3035", "3123"], "types": ["ArmorPenetration", "CriticalStrike", "Damage"], "sell": "2310"}, {"itemId": "3035", "name": "最后的轻语", "price": "750", "total": "1450", "description": "<mainText><stats><attention>20</attention>攻击力<br><attention>18%</attention>护甲穿透</stats><br><br></mainText>", "item_desc": "20攻击力\n18%护甲穿透", "into": ["3033", "3036", "6694"], "from": ["1036", "1036"], "types": ["ArmorPenetration", "Damage"], "sell": "1015"}, {"itemId": "3036", "name": "多米尼克领主的致意", "price": "350", "total": "3100", "description": "<mainText><stats><attention>35</attention>攻击力<br><attention>40%</attention>护甲穿透<br><attention>25%</attention>暴击几率</stats><br><br></mainText>", "item_desc": "35攻击力\n40%护甲穿透\n25%暴击几率", "into": [], "from": ["3035", "6670"], "types": ["ArmorPenetration", "CriticalStrike", "Damage"], "sell": "2170"}, {"itemId": "3039", "name": "阿塔玛的清算", "price": "925", "total": "2900", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br><br><li><passive>大手：</passive>在与敌方英雄、防御塔、或史诗级野怪作战时，造成<physicalDamage><healing>0.5% - 0 (基于处于战斗的秒数)最大生命值</healing>的额外物理伤害</physicalDamage>。</mainText>", "item_desc": "", "into": [], "from": ["1037", "3105"], "types": ["Armor", "Damage", "Lane", "SpellBlock"], "sell": "2030"}, {"itemId": "3041", "name": "梅贾的窃魂卷", "price": "1150", "total": "1500", "description": "<mainText><stats><attention>20</attention>法术强度<br><attention>100</attention>生命值</stats><br><br><passive>荣耀</passive><br><keyword>参与击杀</keyword>后提供<passive>荣耀</passive>层数，至多至25层。阵亡时损失10层<passive>荣耀</passive>。<br>每层<passive>荣耀</passive>提供<scaleAP>5法术强度</scaleAP>，并在10层或以上<passive>荣耀</passive>时提供<speed>10%移动速度</speed>。</mainText>", "item_desc": "20法术强度\n100生命值 \n\n荣耀\n参与击杀后提供荣耀层数，至多至25层。阵亡时损失10层荣耀。\n每次荣耀提供5法术强度，并在10层或以上荣耀时提供10%移动速度。", "into": [], "from": ["1082"], "types": ["Health", "NonbootsMovement", "SpellDamage"], "sell": "1050"}, {"itemId": "3044", "name": "净蚀", "price": "350", "total": "1100", "description": "<mainText><stats><attention>15</attention>攻击力<br><attention>200</attention>生命值</stats><br><br><passive>狂怒</passive><br>攻击会提供持续2秒的<speed>移动速度</speed>。</mainText>", "item_desc": "15攻击力\n200生命值 \n\n狂怒\n【近战】攻击一名单位会提供20移动速度，持续2秒。\n【远程】攻击一名单位会提供10移动速度，持续2秒。", "into": ["3071", "3073", "3078", "6630", "6631"], "from": ["1028", "1036"], "types": ["Damage", "Health", "NonbootsMovement"], "sell": "770"}, {"itemId": "3046", "name": "幻影之舞", "price": "950", "total": "2650", "description": "<mainText><stats><attention>60%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>8%</attention>移动速度</stats><br><br><passive>幽影华尔兹</passive><br>变为<keyword>幽灵</keyword>状态。</mainText>", "item_desc": "60%攻击速度\n25%暴击几率\n8%移动速度 \n\n幽影华尔兹\n变为幽灵状态。", "into": [], "from": ["1042", "1042", "3086"], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "1855"}, {"itemId": "3047", "name": "铁板靴", "price": "600", "total": "1200", "description": "<mainText><stats><attention>25</attention>护甲<br><attention>45</attention>移动速度</stats><br><br><passive>镀板</passive><br>使即将到来的攻击伤害降低10%。</mainText>", "item_desc": "25护甲\n45移动速度 \n镀板\n使即将到来的攻击伤害降低10%。", "into": ["3174"], "from": ["1001", "1029"], "types": ["Armor", "Boots"], "sell": "840"}, {"itemId": "3050", "name": "基克的聚合", "price": "700", "total": "2200", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>25</attention>护甲<br><attention>25</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br><passive>霜火风暴</passive><br>施放你的终极技能时会召唤一个风暴环绕于你5秒。这个风暴对敌方英雄们造成每秒<magicDamage>30魔法伤害</magicDamage>和30%<keyword>减速</keyword>。</mainText>", "item_desc": "300生命值\n25护甲\n25魔法抗性\n10技能急速 \n\n霜火风暴(45秒)\n施放你的终极技能时会召唤一个风暴环绕于你5秒。这个风暴对敌方英雄们造成每秒30魔法伤害和30%减速。", "into": [], "from": ["1029", "1033", "3067"], "types": ["AbilityHaste", "Armor", "Health", "SpellBlock"], "sell": "1540"}, {"itemId": "3051", "name": "缚炉之斧", "price": "250", "total": "1200", "description": "<mainText><stats><attention>20</attention>攻击力<br><attention>20%</attention>攻击速度</stats><br><br></mainText>", "item_desc": "20攻击力\n20%攻击速度", "into": ["3078", "3302", "6672"], "from": ["1036", "1036", "1042"], "types": ["AttackSpeed", "Damage"], "sell": "840"}, {"itemId": "3053", "name": "斯特拉克的挑战护手", "price": "775", "total": "3200", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>20%</attention>韧性</stats><br><br><passive>抓人双爪</passive><br>获得<scaleAD>额外攻击力</scaleAD>。<br><br><passive>救主灵刃</passive><br>在受到将使你的生命值跌到30%以下的伤害时，提供持续4.5秒的<shield>不断衰减的护盾值</shield>。</mainText>", "item_desc": "400生命值\n20%韧性\n\n抓人双爪\n获得(45% 基础【攻击力】 )额外攻击力。\n\n救主灵刃(90秒)\n在受到将使你的生命值跌到30%以下的伤害时，提供在4.5秒的不断衰减的(60%额外【生命值】)护盾值。", "into": [], "from": ["1028", "1037", "2021"], "types": ["Damage", "Health", "Tenacity"], "sell": "2240"}, {"itemId": "3057", "name": "耀光", "price": "650", "total": "900", "description": "<mainText><stats><attention>10</attention>技能急速</stats><br><br><passive>咒刃</passive><br>施放技能后，你的下一次普通攻击会造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>。<br></mainText>", "item_desc": "10技能极速 \n\n咒刃(1.5秒)\n施放技能后，你的下一次普通攻击会造成(100% 基础【攻击力】 )物理伤害。", "into": ["3078", "3100", "6632", "6662"], "from": ["2022"], "types": ["AbilityHaste", "OnHit"], "sell": "630"}, {"itemId": "3065", "name": "振奋盔甲", "price": "650", "total": "2700", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>50</attention>魔法抗性<br><attention>10</attention>技能急速<br><attention>100%</attention>基础生命回复</stats><br><br><passive>无拘活力</passive><br>你身上的治疗和护盾效果提升25%。</mainText>", "item_desc": "400生命值\n50魔法抗性\n10技能急速\n100%基础生命回复 \n\n无拘活力\n你身上的治疗和护盾的效果提升25%。", "into": [], "from": ["3067", "3211"], "types": ["AbilityHaste", "CooldownReduction", "Health", "HealthRegen", "SpellBlock"], "sell": "1890"}, {"itemId": "3066", "name": "带翼的月板甲", "price": "400", "total": "800", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>4%</attention>移动速度</stats><br><br></mainText>", "item_desc": "200生命值\n4%移动速度", "into": ["3002", "3181", "3742", "4401", "323002"], "from": ["1028"], "types": ["Health", "NonbootsMovement"], "sell": "560"}, {"itemId": "3067", "name": "燃烧宝石", "price": "150", "total": "800", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>10</attention>技能急速</stats><br><br></mainText>", "item_desc": "200生命值\n10技能极速", "into": ["3050", "3065", "3071", "3107", "3109", "3119", "3165", "3190", "3222", "4403", "4629", "4644", "6617", "6620", "6630", "6632", "6656", "8001", "8020", "323107", "323109", "323119", "323190", "323222", "326617", "326620", "328020"], "from": ["1028", "2022"], "types": ["AbilityHaste", "CooldownReduction", "Health"], "sell": "560"}, {"itemId": "3068", "name": "日炎圣盾", "price": "1000", "total": "2700", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>50</attention>护甲<br><attention>10</attention>技能急速</stats><br><br><passive>献祭</passive><br>承受或造成伤害后，每秒对附近敌人造成<magicDamage>魔法伤害</magicDamage>，持续3秒。 </mainText>", "item_desc": "350生命值\n50护甲\n10技能急速\n\n献祭\n承受或造成伤害后，每秒对附近敌人造成(20 +1% 额外 【生命值】)魔法伤害，持续3秒。", "into": [], "from": ["1031", "6660"], "types": ["AbilityHaste", "Armor", "<PERSON>ra", "Health"], "sell": "1890"}, {"itemId": "3070", "name": "女神之泪", "price": "400", "total": "400", "description": "<mainText><stats><attention> 240</attention>法力</stats><br><br><passive>法力流</passive>  (8秒，最大4层)<br>技能在命中时会提供<scaleMana>3最大法力值</scaleMana>(对英雄时翻倍)，至多至<scaleMana>360</scaleMana>。<br><br><passive>帮助之手</passive><br>攻击会对小兵造成额外的<physicalDamage>5物理伤害</physicalDamage>。</mainText>", "item_desc": "240法力 \n\n法力流(8秒，最大4层)\n技能在命中时会提供3最大法力值(对英雄时翻倍)，至多至360。\n\n帮助之手\n攻击会对小兵造成额外的5物理伤害。", "into": ["3003", "3004", "3119"], "from": [], "types": ["<PERSON><PERSON>", "ManaRegen"], "sell": "280"}, {"itemId": "3071", "name": "黑色切割者", "price": "225", "total": "3000", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>400</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><passive>切割</passive><br>对英雄造成<physicalDamage>物理伤害</physicalDamage>时会将其<scaleArmor>护甲降低6%</scaleArmor>，持续6秒(可叠加5次)。<br><br><passive>热烈</passive><br>造成<physicalDamage>物理伤害</physicalDamage>时会提供持续2秒的<speed>20移动速度</speed>。</mainText>", "item_desc": "40攻击力\n400生命值\n20技能急速 \n\n切割\n对英雄造成物理伤害时会将其护甲降低6%，持续6秒(可叠加5次)。\n\n热烈\n造成物理伤害时会提供持续2秒的20移动速度。", "into": [], "from": ["1037", "3044", "3067"], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage", "Health", "NonbootsMovement", "OnHit"], "sell": "2100"}, {"itemId": "3072", "name": "饮血剑", "price": "325", "total": "3400", "description": "<mainText><stats><attention>80</attention>攻击力<br><attention>15%</attention>生命偷取</stats><br><br><passive>灵液护盾</passive><br>将来自你的生命偷取的溢出治疗效果转化为<shield>护盾值</shield>。</mainText>", "item_desc": "80攻击力\n15%生命偷取 \n\n灵液护盾\n将来自你的生命偷取的溢出治疗效果转化为护盾值，至多(165-315【等级提升】)。", "into": [], "from": ["1037", "1038", "1053"], "types": ["Damage", "LifeSteal"], "sell": "2380"}, {"itemId": "3073", "name": "海克斯注力刚壁", "price": "500", "total": "3000", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>20%</attention>攻击速度<br><attention>450</attention>生命值</stats><br><br><passive>海克斯充能</passive><br>获得30终极技能急速。<br><br><passive>过载</passive><br>在施放你的终极技能后，获得持续8秒的<attackSpeed>30%攻击速度</attackSpeed>和<speed>15%移动速度</speed>。 </mainText>", "item_desc": "40攻击力\n20%攻击速度\n450生命值 \n\n海克斯充能\n获得30终极技能急速\n\n过载(30秒)\n在施放你的终极技能后，获得持续8秒的30%攻击速度和15%移动速度。\n\n控场可能是一件棘手的工作。", "into": [], "from": ["1042", "2021", "3044"], "types": ["AbilityHaste", "AttackSpeed", "CooldownReduction", "Damage", "Health", "NonbootsMovement"], "sell": "2100"}, {"itemId": "3074", "name": "贪欲九头蛇", "price": "150", "total": "3300", "description": "<mainText><stats><attention>65</attention>攻击力<br><attention>15</attention>技能急速<br><attention>12%</attention>生命偷取</stats><br><br><passive>顺劈</passive><br>攻击会对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。 <active>血斩</active><br>对你周围的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br>你的生命偷取会作用于此伤害。</mainText>", "item_desc": "65攻击力\n15技能急速\n12%生命偷取 \n\n顺劈\n【近战】攻击会对附近的敌人们造成(40%)物理伤害\n【远程】攻击会对附近的敌人们造成(20%)物理伤害\n\n血斩(10秒)\n【近战】对你周围的敌人造成(80%【攻击力】)物理伤害，你的生命偷取会作用于此伤害。\n【远程】对你周围的敌人造成(80%【攻击力】)物理伤害。你的生命偷取会作用于此伤害。\n\n顺劈会受益于生命偷取。\n顺劈不会在建筑物上触发。", "into": [], "from": ["1053", "3077", "3133"], "types": ["AbilityHaste", "CooldownReduction", "Damage", "LifeSteal", "OnHit"], "sell": "2310"}, {"itemId": "3075", "name": "荆棘之甲", "price": "450", "total": "2450", "description": "<mainText><stats><attention>150</attention>生命值<br><attention>75</attention>护甲</stats><br><br><passive>荆棘</passive><br>在被一次攻击命中后，对攻击者造成<magicDamage>魔法伤害</magicDamage>，并且如果目标是英雄，还会施加持续3秒的40%<keyword>重伤</keyword>效果。</mainText>", "item_desc": "150生命值\n75护甲 \n\n荆棘\n在被一次攻击命中后，对攻击者造成(20+10%额外【护甲】)魔法伤害，并且如果目标是英雄，还会施加持续3秒的40%重伤效果。", "into": [], "from": ["1028", "1031", "3076"], "types": ["Armor", "Health"], "sell": "1715"}, {"itemId": "3076", "name": "棘刺背心", "price": "200", "total": "800", "description": "<mainText><stats><attention>30</attention>护甲</stats><br><br><passive>荆棘</passive><br>在被一次攻击命中后，对攻击者造成<magicDamage>0魔法伤害</magicDamage>并且如果目标是英雄，还会对其施加持续3秒的<keyword>40%重伤</keyword>效果。</mainText>", "item_desc": "30护甲 \n\n荆棘\n在被一次攻击命中后，对攻击者造成(10)法伤害并且如果目标是英雄，还会对其施加持续3秒的40%重伤效果。\n\n重伤:降低治疗效果和生命回复的效能，", "into": ["3075", "323075"], "from": ["1029", "1029"], "types": ["Armor"], "sell": "560"}, {"itemId": "3077", "name": "提亚马特", "price": "500", "total": "1200", "description": "<mainText><stats><attention>20</attention>攻击力</stats><br><br><passive>顺劈</passive><br>攻击会对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br> <active>新月</active><br>对你周围的敌人们造成<physicalDamage>物理伤害</physicalDamage>。</mainText>", "item_desc": "20攻击力 \n\n顺劈\n攻击会对附近的敌人们造成(【近战】(40%【攻击力】)|【远程】(20%【攻击力】))物理伤害。\n\n新月(10秒)\n对你周围的敌人们造成(75%【攻击力】)物理伤害。\n\n顺劈不会在建筑物上触发。", "into": ["3074", "3748", "6631", "6698"], "from": ["1036", "1036"], "types": ["Damage", "OnHit"], "sell": "840"}, {"itemId": "3078", "name": "三相之力", "price": "133", "total": "3333", "description": "<mainText><stats><attention>36</attention>攻击力<br><attention>30%</attention>攻击速度<br><attention>333</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>咒刃</passive><br>施放技能后，你的下一次普通攻击会造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>。<br> <br><passive>加快</passive><br>攻击一个单位时会提供持续2秒的<speed>20移动速度</speed>。</mainText>", "item_desc": "36攻击力\n30%攻击速度\n333生命值\n15技能急速 \n\n咒刃(1.5秒)\n施放技能后，你的下一次普通攻击会造成(200% 基础【攻击力】 )物理伤害。\n\n加快\n攻击一个单位时会提供持续2秒的20移动速度。", "into": [], "from": ["3044", "3051", "3057"], "types": ["AbilityHaste", "AttackSpeed", "CooldownReduction", "Damage", "Health", "NonbootsMovement", "OnHit"], "sell": "2333"}, {"itemId": "3082", "name": "守望者铠甲", "price": "400", "total": "1000", "description": "<mainText><stats><attention>40</attention>护甲</stats><br><br><passive>坚如磐石</passive><br>使所受的来自攻击的伤害降低。</mainText>", "item_desc": "40护甲 \n坚如磐石\n使所受的来自英雄攻击的伤害降低15。\n对单次攻击的格挡数额不会超过该次攻击的20%伤害。", "into": ["3110", "3143", "323110"], "from": ["1029", "1029"], "types": ["Armor"], "sell": "700"}, {"itemId": "3083", "name": "狂徒铠甲", "price": "500", "total": "3100", "description": "<mainText><stats><attention>1000</attention>生命值<br><attention>100%</attention>基础生命回复</stats><br><br><passive>狂徒之心</passive> <br>如果你拥有2000额外生命值，并且在8秒内没有受到伤害，则每秒回复<healing>0</healing>生命值。<br><br><passive>狂徒之活力</passive><br>获得额外生命值，相当于你12%的装备生命值(<healing>0</healing>)。</mainText>", "item_desc": "1000生命值\n100%基础生命回复 \n狂徒之心\n如果你拥有2000额外生命值，并且在8秒内没有受到伤害，则每\n秒回复(3%【生命值】)生命值。\n狂徒之活力\n获得额外生命值，相当于你12%的装备生命值。\n非英雄单位的伤害会使【狂徒之心】失能3秒。", "into": [], "from": ["1011", "1011", "3801"], "types": ["Health", "HealthRegen"], "sell": "2170"}, {"itemId": "3084", "name": "心之钢", "price": "400", "total": "3000", "description": "<mainText><stats><attention>900</attention>生命值<br><attention>100%</attention>基础生命回复</stats><br><br><passive>庞然吞食</passive> 每个目标 (0秒)<br>如果一个敌方英雄在附近度过了若干秒，那么你的下一次对其打出的攻击会造成<physicalDamage>70外加你的6%</physicalDamage>最大生命值的<physicalDamage>额外物理伤害</physicalDamage>，并为你提供相当于<scaleHealth>8%</scaleHealth>该伤害的<scaleHealth>最大生命值</scaleHealth>。<br><br><passive>歌利亚巨人</passive><br>每有<scaleHealth>1000最大生命值</scaleHealth>，获得3%体型提升，至多至30%。 </mainText>", "item_desc": "900生命值\n100%基础生命回复 \n庞然吞食 每个目标(30秒)\n如果一个敌方英雄在附近度过了3秒，那么你的下一次对其打出的攻击会造成70外加你的6%最大生命值的额外物理伤害，并为你提供相当于8%该伤害的最大生命值。\n歌利亚巨人\n每有1000最大生命值，获得3%体型提升，至多至30%。", "into": [], "from": ["1011", "1011", "3801"], "types": ["Health", "HealthRegen"], "sell": "2100"}, {"itemId": "3085", "name": "卢安娜的飓风", "price": "850", "total": "2650", "description": "<mainText><stats><attention>40%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br><passive>风怒</passive><br>攻击对目标附近的2个额外敌人发射弩箭。<br>每根弩箭造成<physicalDamage>物理伤害</physicalDamage>并施加<OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "必须是远程英雄\n40%攻击速度\n25%暴击几率\n4%移动速度 \n风怒\n攻击对目标附近的2个额外敌人发射弩箭。\n每根弩箭造成(55%【攻击力】)物理伤害并施加攻击特效。\n风怒可以暴击。", "into": [], "from": ["3086", "3144"], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement", "OnHit"], "sell": "1855"}, {"itemId": "3086", "name": "狂热", "price": "350", "total": "1200", "description": "<mainText><stats><attention>15%</attention>攻击速度<br><attention>15%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br></mainText>", "item_desc": "15%攻击速度\n15%暴击几率\n4%移动速度", "into": ["3046", "3085", "3094", "4403", "6671", "6675"], "from": ["1018", "1042"], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "840"}, {"itemId": "3087", "name": "斯塔缇克电刃", "price": "450", "total": "2700", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>30%</attention>攻击速度<br><attention>4%</attention>移动速度</stats><br><br><passive>电火花</passive><br>攻击<OnHit>命中时</OnHit>会触发连锁闪电，造成<magicDamage>魔法伤害</magicDamage>，有冷却时间。<br><br><passive>电击</passive><br>在对目标造成伤害的3秒内参与击杀，会重置电火花的冷却时间。</mainText>", "item_desc": "45攻击力\n30%攻击速度\n4%移动速度 \n电火花(25=(25-10【随等级提升】)秒)\n在8秒内的前3次攻击在命中时会发射连锁闪电，对最多5个目标造成60魔法伤害，攻击小兵和野怪时提升至85魔法伤害。冷却时间从第一次攻击开始计算。\n电火花\n在对目标造成伤害的3秒内参与击杀，会重置电火花的冷却时间。", "into": [], "from": ["1037", "3144", "6690"], "types": ["AttackSpeed", "Damage", "NonbootsMovement", "OnHit"], "sell": "1890"}, {"itemId": "3089", "name": "灭世者的死亡之帽", "price": "1100", "total": "3500", "description": "<mainText><stats><attention>130</attention>法术强度</stats><br><br><passive>魔法乐章</passive><br>使你的总<scaleAP>法术强度提升30%</scaleAP>。</mainText>", "item_desc": "130法术强度 \n魔法乐章\n使你的总法术强度提升30%。", "into": [], "from": ["1058", "1058"], "types": ["SpellDamage"], "sell": "2450"}, {"itemId": "3091", "name": "智慧末刃", "price": "550", "total": "2800", "description": "<mainText><stats><attention>50%</attention>攻击速度<br><attention>45</attention>魔法抗性<br><attention>20%</attention>韧性</stats><br><br><passive>喧争</passive><br>普攻造成<magicDamage>额外魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "50%攻击速度\n45魔法抗性\n20%韧性 \n喧争\n普攻造成45额外魔法伤害攻击特效。", "into": [], "from": ["1043", "1043", "1057"], "types": ["AttackSpeed", "OnHit", "SpellBlock", "Tenacity"], "sell": "1960"}, {"itemId": "3094", "name": "疾射火炮", "price": "850", "total": "2650", "description": "<mainText><stats><attention>35%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br><passive>神射手</passive><br>你的<keyword>盈能攻击</keyword>造成<magicDamage>40额外魔法伤害</magicDamage>并获得35%额外攻击距离。</mainText>", "item_desc": "35%攻击速度\n25%暴击几率\n4%移动速度 \n神射手\n你的盈能攻击造成40额外魔法伤害并获得35%额外攻击距离。\n攻击距离的提升不能多于150码。\n盈能:移动和攻击将生成一次盈能攻击。", "into": [], "from": ["3086", "3144"], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "1855"}, {"itemId": "3095", "name": "岚切", "price": "500", "total": "3100", "description": "<mainText><stats><attention>65</attention>攻击力<br><attention>0%</attention>攻击速度<br><attention>25%</attention>暴击几率</stats><br><br><passive>暴击更狠</passive><br>暴击攻击造成额外伤害。</mainText>", "item_desc": "", "into": [], "from": ["1018", "2015", "6670"], "types": ["CriticalStrike", "Damage"], "sell": "2170"}, {"itemId": "3100", "name": "巫妖之祸", "price": "250", "total": "2900", "description": "<mainText><stats><attention>100</attention>法术强度<br><attention>4%</attention>移动速度<br><attention>10</attention>技能急速</stats><br><br><passive>咒刃</passive><br>施放技能后，你的下一次攻击会造成<magicDamage>额外魔法伤害</magicDamage><OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "100法术强度\n4%移动速度\n10技能急速 \n咒刃(1.5秒)\n施放技能后，你在10秒内的下一次普通攻击会获得50%攻击速度并造成(75% 基础 【攻击力】+40%【法术强度】)额外魔法伤害攻击特效。", "into": [], "from": ["1026", "3057", "3113"], "types": ["AbilityHaste", "NonbootsMovement", "OnHit", "SpellDamage"], "sell": "2030"}, {"itemId": "3102", "name": "女妖面纱", "price": "200", "total": "3000", "description": "<mainText><stats><attention>105</attention>法术强度<br><attention>40</attention>魔法抗性</stats><br><br><passive>废除</passive><br>提供一层法术护盾来格挡下一个敌方技能。</mainText>", "item_desc": "105法术强度\n40魔法抗性 \n废除(40秒)\n提供一层法术护盾来格挡下一个敌方技能。\n如果在此装备冷却完毕之前受到来自英雄的伤害，那么它的冷却时间会重新开始计算。", "into": [], "from": ["1058", "4632"], "types": ["SpellBlock", "SpellDamage"], "sell": "2100"}, {"itemId": "3105", "name": "军团圣盾", "price": "400", "total": "1100", "description": "<mainText><stats><attention>25</attention>护甲<br><attention>25</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br></mainText>", "item_desc": "", "into": ["2502", "3039", "3193", "4403", "6667", "323050"], "from": ["1029", "1033"], "types": ["AbilityHaste", "Armor", "CooldownReduction", "SpellBlock"], "sell": "770"}, {"itemId": "3107", "name": "救赎", "price": "900", "total": "2300", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>15</attention>技能急速<br><attention>100%</attention>基础法力回复<br><attention> 10%</attention>治疗和护盾强度</stats><br><br> <active>干涉</active> <br>在2.5秒后，为友方单位们回复<healing>200 - 400生命值</healing>，并对敌方英雄们造成<trueDamage>10%最大生命值的真实伤害</trueDamage>。</mainText>", "item_desc": "200生命值\n15技能急速\n100%基础法力回复\n10%治疗和护盾强度 \n干涉(90秒)\n在2.5秒后，为友方英雄们回复200-400(友军【等级提升】)生命值，并对敌方英雄们造成10%最大生命值的真实伤害。\n可以在死亡时主动使用。\n对相同目标的后续干涉效果降低50%。", "into": [], "from": ["3067", "3114"], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen"], "sell": "1610"}, {"itemId": "3108", "name": "恶魔法典", "price": "200", "total": "850", "description": "<mainText><stats><attention>25</attention>法术强度<br><attention>10</attention>技能急速</stats><br><br></mainText>", "item_desc": "25法术强度\n10技能急速", "into": ["3003", "3115", "3128", "3137", "3152", "4005", "4628", "4629", "4633", "4636", "6616", "8010", "323003", "324005", "326616"], "from": ["1052", "2022"], "types": ["AbilityHaste", "CooldownReduction", "SpellDamage"], "sell": "595"}, {"itemId": "3109", "name": "骑士之誓", "price": "400", "total": "2300", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>40</attention>护甲<br><attention>10</attention>技能急速<br><attention>100%</attention>基础生命回复</stats><br><br><passive>牺牲</passive><br>当你的<attention>誓约者</attention>在附近时，将其所受的12%伤害转移到你身上，并为你提供相当于其对英雄的10%已造成伤害值的<healing>治疗效果</healing>。 <active>立誓</active> (0秒)<br>指定一名友方英雄作为你的<attention>誓约者</attention>。</mainText>", "item_desc": "200生命值\n40护甲\n10技能急速\n100%基础生命回复 \n牺牲\n当你的誓约者在附近时，将其所受的12%伤害转移到你身上，并为你提供相当于其对英雄的10%已造成伤害值的治疗效果。\n立誓(60秒)\n指定一名友方英雄作为你的誓约者。", "into": [], "from": ["1006", "1031", "3067"], "types": ["AbilityHaste", "Active", "Armor", "<PERSON>ra", "CooldownReduction", "Health", "HealthRegen"], "sell": "1610"}, {"itemId": "3110", "name": "冰霜之心", "price": "600", "total": "2500", "description": "<mainText><stats><attention>75</attention>护甲<br><attention> 400</attention>法力<br><attention>20</attention>技能急速</stats><br><br><passive>凛冬之抚</passive><br>使附近敌方英雄的<attackSpeed>攻击速度</attackSpeed>降低<attackSpeed>20%</attackSpeed>。</mainText>", "item_desc": "75护甲\n400法力\n20技能急速 \n凛冬之抚\n使附近敌方英雄的攻击速度降低20%。", "into": [], "from": ["3024", "3082"], "types": ["AbilityHaste", "Armor", "<PERSON>ra", "CooldownReduction", "<PERSON><PERSON>"], "sell": "1750"}, {"itemId": "3111", "name": "水银之靴", "price": "550", "total": "1250", "description": "<mainText><stats><attention>20</attention>魔法抗性<br><attention>45</attention>移动速度<br><attention>30%</attention>韧性</stats><br><br></mainText>", "item_desc": "20魔法抗性\n45移动速度\n30%韧性 \n韧性:缩短受到的晕眩、减速、嘲讽、恐惧、沉默、致盲、昏睡、变形和定身的持续时间。", "into": ["3173"], "from": ["1001", "1033"], "types": ["Boots", "SpellBlock", "Tenacity"], "sell": "875"}, {"itemId": "3112", "name": "守护者法球", "price": "950", "total": "950", "description": "<mainText><stats><attention>50</attention>法术强度<br><attention>150</attention>生命值</stats><br><br><br><li><passive>复原力：</passive>每5秒回复<scaleMana>10法力值</scaleMana>。如果你不能获得法力值，则转而回复<healing>15生命值</healing>。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "Lane", "ManaRegen", "SpellDamage"], "sell": "665"}, {"itemId": "3113", "name": "以太精魂", "price": "500", "total": "900", "description": "<mainText><stats><attention>30</attention>法术强度<br><attention>4%</attention>移动速度</stats><br><br></mainText>", "item_desc": "30法术强度\n4%移动速度", "into": ["2065", "3100", "3504", "4629", "4646", "322065", "323504"], "from": ["1052"], "types": ["NonbootsMovement", "SpellDamage"], "sell": "630"}, {"itemId": "3114", "name": "禁忌雕像", "price": "400", "total": "600", "description": "<mainText><stats><attention>50%</attention>基础法力回复<br><attention> 8%</attention>治疗和护盾强度</stats><br><br></mainText>", "item_desc": "50%基础法力回复\n8%治疗和护盾强度", "into": ["3011", "3107", "3222", "3504", "6616", "6621", "223011", "323107", "323222", "323504", "326616", "326621"], "from": ["1004"], "types": ["ManaRegen"], "sell": "420"}, {"itemId": "3115", "name": "纳什之牙", "price": "500", "total": "2900", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>50%</attention>攻击速度<br><attention>15</attention>技能急速</stats><br><br><passive>艾卡西亚之咬</passive><br>普攻造成<magicDamage>额外魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "80法术强度\n50%攻击速度\n15技能急速 \n艾卡西亚之咬\n普攻造成(15 +15%【法术强度】)额外魔法伤害攻击特效。", "into": [], "from": ["1026", "1043", "3108"], "types": ["AbilityHaste", "AttackSpeed", "OnHit", "SpellDamage"], "sell": "2030"}, {"itemId": "3116", "name": "瑞莱的冰晶节杖", "price": "450", "total": "2600", "description": "<mainText><stats><attention>65</attention>法术强度<br><attention>400</attention>生命值</stats><br><br><passive>凝霜</passive><br>伤害型技能会使敌人<keyword>减速</keyword>30%，持续1秒。</mainText>", "item_desc": "65法术强度\n400生命值 \n凝霜\n伤害型技能会使敌人减速30%，持续1秒。\n减速:移动速度被降低了。", "into": [], "from": ["1011", "1026", "1052"], "types": ["Health", "Slow", "SpellDamage"], "sell": "1820"}, {"itemId": "3118", "name": "残疫", "price": "650", "total": "2700", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention> 600</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>蔑视</passive><br>获得20终极技能急速。<br><br><passive>憎恨之雾</passive><br>在用你的终极技能对一个英雄造成伤害时，灼烧其脚下的地面3秒，每秒造成<magicDamage>魔法伤害</magicDamage>并削减其<scaleMR>魔法抗性</scaleMR>。<br></mainText>", "item_desc": "90法术强度\n600法力\n15技能急速 \n蔑视\n获得20终极技能急速。\n憎恨之雾\n在用你的终极技能对一个英雄造成伤害时，灼烧其脚下的地面3秒，每秒造成(60+5%【法术强度】)魔法伤害并削减其(10)魔法抗性。\n半径会基于造成的伤害值来提升。\n普攻不能触发【憎恨之雾】。", "into": [], "from": ["1026", "3802"], "types": ["AbilityHaste", "<PERSON><PERSON>", "SpellDamage"], "sell": "1890"}, {"itemId": "3119", "name": "凛冬之临", "price": "300", "total": "2400", "description": "<mainText><stats><attention>550</attention>生命值<br><attention> 500</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得<scaleHealth>0生命值</scaleHealth>。<br><br><passive>法力流</passive>  (8秒，最大4层)<br>普攻和技能在命中时会提供<scaleMana>3最大法力值</scaleMana>(对英雄时翻倍)。<br>在<scaleMana>360最大法力值</scaleMana>时转变为<rarityLegendary>末日寒冬</rarityLegendary>。</mainText>", "item_desc": "550生命值\n500法力\n15技能急速 \n敬畏\n获得(15% 额外【法力值】)生命值。\n法力流(8秒，最大4层)\n普攻和技能在命中时会提供3最大法力值(对英雄时翻倍)。\n在360最大法力值时转变为末日寒冬。", "into": [], "from": ["1011", "3067", "3070"], "types": ["AbilityHaste", "Health", "<PERSON><PERSON>"], "sell": "1680"}, {"itemId": "3123", "name": "死刑宣告", "price": "450", "total": "800", "description": "<mainText><stats><attention>15</attention>攻击力</stats><br><br><passive>重伤</passive><br>对敌方英雄造成物理伤害时会施加持续3秒的<keyword>40%重伤</keyword>效果。</mainText>", "item_desc": "15攻击力 \n重伤\n对敌方英雄造成物理伤害时会施加持续3秒的40%重伤效果。\n重伤:降低治疗效果和生命回复的效能。", "into": ["3033", "6609"], "from": ["1036"], "types": ["Damage"], "sell": "560"}, {"itemId": "3124", "name": "鬼索的狂暴之刃", "price": "1025", "total": "3000", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>30</attention>法术强度<br><attention>25%</attention>攻击速度</stats><br><br><passive>怨怒</passive><br>攻击造成<magicDamage>30额外魔法伤害<OnHit>攻击特效</OnHit></magicDamage>。<br><br><passive>沸腾打击</passive><br>攻击会提供<attackSpeed>8%攻击速度</attackSpeed>，持续3秒(可叠加4次)。<br>在满层状态下，每第三次攻击会附带2次<OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "30攻击力\n30法术强度\n25%攻击速度 \n怨怒\n攻击造成30额外魔法伤害攻击特效。\n沸腾打击\n攻击会提供8%攻击速度，持续3秒(可叠加4次)。\n在满层状态下，每第三次攻击会附带2次攻击特效。", "into": [], "from": ["1037", "1043", "1052"], "types": ["AttackSpeed", "Damage", "OnHit", "SpellDamage"], "sell": "2100"}, {"itemId": "3128", "name": "冥火之拥", "price": "850", "total": "2900", "description": "<mainText><stats><attention>120</attention>法术强度<br><attention>10</attention>技能急速</stats><br><br><br><br><active>主动 - </active> <active>沉默：</active>造成相当于目标<magicDamage>15%最大生命值</magicDamage>的魔法伤害，然后使其所受伤害增幅15%，持续4秒(90 (0秒))。</mainText>", "item_desc": "", "into": [], "from": ["1058", "3108"], "types": ["CooldownReduction", "SpellDamage"], "sell": "2030"}, {"itemId": "3131", "name": "神圣之剑", "price": "800", "total": "2300", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>25%</attention>攻击速度<br><attention>18</attention>穿甲</stats><br><br><br><br><active>主动 - </active> <active>神圣祝福：</active>提供<attackSpeed>100%攻击速度</attackSpeed> 和100%暴击几率，持续3秒或3次普攻(90 (0秒))。</mainText>", "item_desc": " ", "into": [], "from": ["1042", "1042", "3134"], "types": ["AttackSpeed", "CriticalStrike", "Damage"], "sell": "1610"}, {"itemId": "3133", "name": "考尔菲德的战锤", "price": "100", "total": "1050", "description": "<mainText><stats><attention>20</attention>攻击力<br><attention>10</attention>技能急速</stats><br><br></mainText>", "item_desc": "20攻击力\n10技能急速", "into": ["3004", "3074", "3156", "3179", "3508", "4402", "6333", "6609", "6610", "6632", "6691", "6692", "6693", "6694", "6696", "6697", "126697", "323004"], "from": ["1036", "1036", "2022"], "types": ["AbilityHaste", "CooldownReduction", "Damage"], "sell": "735"}, {"itemId": "3134", "name": "锯齿短匕", "price": "300", "total": "1000", "description": "<mainText><stats><attention>20</attention>攻击力<br><attention>10</attention>穿甲</stats><br><br></mainText>", "item_desc": "20攻击力\n10穿甲", "into": ["3131", "3142", "3179", "3814", "4004", "6676", "6691", "6693", "6695", "6697", "6701", "126697"], "from": ["1036", "1036"], "types": ["ArmorPenetration", "Damage"], "sell": "700"}, {"itemId": "3135", "name": "虚空之杖", "price": "1050", "total": "3000", "description": "<mainText><stats><attention>95</attention>法术强度<br><attention>40%</attention>法术穿透</stats><br><br></mainText>", "item_desc": "95法术强度\n40%法术穿透", "into": [], "from": ["1026", "4630"], "types": ["MagicPenetration", "SpellDamage"], "sell": "2100"}, {"itemId": "3137", "name": "蜕生", "price": "200", "total": "3000", "description": "<mainText><stats><attention>75</attention>法术强度<br><attention>30%</attention>法术穿透<br><attention>20</attention>技能急速</stats><br><br><passive>死中焕生</passive><br>每当一个敌方英雄在被你造成伤害后的3秒内阵亡时，在其所在位置生成一个新星，来为友军们<healing>治疗生命值</healing>。</mainText>", "item_desc": "75法术强度\n30%法术穿透\n20技能急速 \n死中焕生(60秒)\n每当一个敌方英雄在被你造成伤害后的3秒内阵亡时，在其所在位置生成一个新星，来为友军们治疗(100 +20%【法术强度】)生命值。\n死中焕生不能在你阵亡期间触发。\n来自死亡的，是生命。", "into": [], "from": ["3108", "3108", "4630"], "types": ["AbilityHaste", "MagicPenetration", "SpellDamage"], "sell": "2100"}, {"itemId": "3139", "name": "水银弯刀", "price": "125", "total": "3200", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>40</attention>魔法抗性<br><attention>10%</attention>生命偷取</stats><br><br><br><br> <active>主动</active> <br><active>水银</active><br>移除所有控制类减益效果(<keyword>浮空</keyword>除外)，并提供移动速度。</mainText>", "item_desc": "40攻击力\n40魔法抗性\n10%生命偷取 \n水银(90秒)\n主动使用以移除所有控制类减益效果(浮空除外)，并提供持续1.5秒的50%移动速度。\n浮空:被击到空中。在此期间无法移动、攻击、施放技能或使用装备的主动效果", "into": [], "from": ["1037", "1053", "3140"], "types": ["Active", "Damage", "LifeSteal", "NonbootsMovement", "SpellBlock", "Tenacity"], "sell": "2240"}, {"itemId": "3140", "name": "水银饰带", "price": "900", "total": "1300", "description": "<mainText><stats><attention>30</attention>魔法抗性</stats><br><br> <active>水银</active><br>移除所有控制类减益效果(<keyword>滞空</keyword>除外)。</mainText>", "item_desc": "30魔法抗性 \n水银(90秒)\n移除所有控制类减益效果(滞空除外)。\n浮空:被击到空中。在此期间无法移动、攻击、施放技能或使用装备的主动效果。", "into": ["3139", "6035"], "from": ["1033"], "types": ["Active", "SpellBlock"], "sell": "910"}, {"itemId": "3142", "name": "幽梦之灵", "price": "675", "total": "2800", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>18</attention>穿甲<br><attention>4%</attention>移动速度</stats><br><br><passive>鬼影萦绕</passive><br>非战斗状态期间获得<speed>0移动速度</speed>。<br><br> <active>鬼魂步伐</active><br>提供<speed>移动速度</speed>和<keyword>幽灵状态</keyword>，持续0秒。</mainText>", "item_desc": "55攻击力\n18穿甲 \n4%移动速度\n鬼影萦绕\n提供非战斗状态下的(20)移动速度\n鬼魂步伐(45秒)\n提供【近战】20%移动速度和幽灵状态，持续6秒。\n提供【远程】10%移动速度和幽灵状态，持续4秒。\n幽灵:无视其它单位的碰撞体积。", "into": [], "from": ["1036", "3134", "6690"], "types": ["Active", "ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "1960"}, {"itemId": "3143", "name": "兰顿之兆", "price": "800", "total": "2700", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>75</attention>护甲</stats><br><br><passive>复原力</passive><br>所受的来自暴击的伤害降低30%。<br> <active>谦卑</active><br>对附近敌人造成持续2秒的70%<keyword>减速</keyword>。</mainText>", "item_desc": "350生命值\n75护甲 \n复原力\n所受的来自暴击的伤害降低30%\n谦卑(90秒)\n对附近敌人造成持续2秒的70%减速。", "into": [], "from": ["1011", "3082"], "types": ["Active", "Armor", "Health", "Slow"], "sell": "1890"}, {"itemId": "3144", "name": "斥候弹弓", "price": "100", "total": "600", "description": "<mainText><stats><attention>20%</attention>攻击速度</stats><br><br><passive>靶心</passive><br>对一名敌方英雄造成伤害时会造成<magicDamage>额外魔法伤害</magicDamage>。<br>攻击可使这个冷却时间缩减1秒。</mainText>", "item_desc": "20%攻击速度 \n靶心(40秒)\n对一名敌方英雄造成伤害时会造成(40)额外魔法伤害。\n攻击可使这个冷却时间缩减1秒。", "into": ["3032", "3085", "3087", "3094"], "from": ["1042", "1042"], "types": ["AttackSpeed"], "sell": "420"}, {"itemId": "3145", "name": "海克斯科技发电机", "price": "300", "total": "1100", "description": "<mainText><stats><attention>45</attention>法术强度</stats><br><br><passive>转速加快</passive><br>对一名敌方英雄造成伤害时会造成<magicDamage>额外魔法伤害</magicDamage>。</mainText>", "item_desc": "45法术强度 \n转速加快(40秒)\n对一名敌方英雄造成伤害时会造成(65)额外魔法伤害", "into": ["3152", "4636", "4645", "4646", "6655"], "from": ["1052", "1052"], "types": ["SpellDamage"], "sell": "770"}, {"itemId": "3146", "name": "海克斯科技枪刃", "price": "750", "total": "3250", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>40</attention>攻击力<br><attention> 15%</attention>全能吸血</stats><br><br><br><br> <active>主动</active>  (0秒)<br><active>闪电弹</active><br>震击目标敌方英雄，造成0魔法伤害和持续2秒的40%减速。</mainText>", "item_desc": "", "into": [], "from": ["1038", "1058"], "types": ["Active", "Damage", "LifeSteal", "SpellDamage", "SpellVamp"], "sell": "2275"}, {"itemId": "3147", "name": "幽魂面具", "price": "500", "total": "1300", "description": "<mainText><stats><attention>30</attention>法术强度<br><attention>200</attention>生命值</stats><br><br><passive>癫狂</passive><br>在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至6%。 </mainText>", "item_desc": "30法术强度\n200生命值 \n癫狂\n在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至6%。", "into": ["4633", "6653", "8010"], "from": ["1028", "1052"], "types": ["Health", "SpellDamage"], "sell": "910"}, {"itemId": "3152", "name": "海克斯科技火箭腰带", "price": "300", "total": "2650", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention>300</attention>生命值<br><attention>20</attention>技能急速</stats><br><br> <active>超音速</active><br>朝着目标方向冲刺，释放多颗魔法弹，魔法弹造成<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "70法术强度\n300生命值\n20技能急速 \n超音速(40秒)\n朝着目标方向冲刺，释放多颗魔法弹，魔法弹造成(100+10%【法术强度】)魔法伤害\n超音速的冲刺无法穿过地形。", "into": [], "from": ["1028", "3108", "3145"], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health", "NonbootsMovement", "SpellDamage"], "sell": "1855"}, {"itemId": "3153", "name": "破败王者之刃", "price": "725", "total": "3200", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>25%</attention>攻击速度<br><attention>10%</attention>生命偷取</stats><br><br><passive>雾之锋</passive><br>攻击造成<physicalDamage>一部分</physicalDamage>敌人当前生命值的<physicalDamage>额外物理伤害</physicalDamage> <OnHit>攻击特效</OnHit>。<br><br><passive>抓挠之影</passive><br>攻击一位英雄3次会对其造成持续1秒的30%<status>减速</status>。</mainText>", "item_desc": "40攻击力\n25%攻击速度\n10%生命偷取 \n雾之锋\n【近战】攻击附带额外的(8%)敌人当前生命值的物理伤害 攻击特效。\n【远程】攻击附带额外的(5%)敌人当前生命值的物理伤害 攻击特效。\n抓挠之影(15秒)\n攻击一位英雄3次会对其造成持续1秒的30%减速。\n雾之锋对小兵和野怪造成的最大伤害为100。\n装备对近战携带者和远程携带者会有不同的性能。", "into": [], "from": ["1037", "1043", "1053"], "types": ["AttackSpeed", "Damage", "LifeSteal", "OnHit", "Slow"], "sell": "2240"}, {"itemId": "3155", "name": "海克斯饮魔刀", "price": "200", "total": "1300", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>25</attention>魔法抗性</stats><br><br><passive>救主灵刃</passive><br>在受到将使你的生命值跌到30%以下的魔法伤害时，提供持续2.5秒的<shield>魔法伤害护盾</shield>。</mainText>", "item_desc": "25攻击力\n25魔法抗性 \n救主灵刃(90秒)\n【近战】在受到将使你的生命值跌到30%以下的魔法伤害时，提供持续2.5秒的(110-280【等级提升】)魔法伤害护盾。\n【远程】在受到将使你的生命值跌到30%以下的魔法伤害时，提供持续2.5秒的(83-210【等级提升】)魔法伤害护盾。", "into": ["3156"], "from": ["1033", "1036", "1036"], "types": ["Damage", "SpellBlock"], "sell": "910"}, {"itemId": "3156", "name": "玛莫提乌斯之噬", "price": "750", "total": "3100", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>15</attention>技能急速<br><attention>40</attention>魔法抗性</stats><br><br><passive>救主灵刃</passive><br>在受到将使你的生命值跌到30%以下的魔法伤害时，提供持续3秒的<shield>魔法伤害护盾</shield>和持续到战斗状态结束的<lifeSteal>10%全能吸血</lifeSteal>。</mainText>", "item_desc": "60攻击力\n15技能急速\n40魔法抗性 \n救主灵刃(90秒)\n【近战】在受到将使你的生命值跌到30%以下的魔法伤害时，提供持续3秒的(200 +150% 额外【攻击力】)魔法伤害护盾和持续到战斗状态结束的10%全能吸血\n【远程】在受到将使你的生命值跌到30%以下的魔法伤害时，提供持续3秒的(150 +112.5% 额外【攻击力】)魔法伤害护盾和持续到战斗状态结束的10%全能吸血", "into": [], "from": ["3133", "3155"], "types": ["AbilityHaste", "Damage", "LifeSteal", "SpellBlock", "SpellVamp"], "sell": "2170"}, {"itemId": "3157", "name": "中娅沙漏", "price": "450", "total": "3250", "description": "<mainText><stats><attention>105</attention>法术强度<br><attention>50</attention>护甲</stats><br><br><br> <br><active>时间停止</active><br>进入<keyword>凝滞</keyword>状态2.5秒。</mainText>", "item_desc": "105法术强度\n50护甲 \n时间停止(120秒)\n进入凝滞状态2.5秒。\n凝滞:变得免疫伤害且不可被选取。在此期间无法移动。", "into": [], "from": ["1058", "2420"], "types": ["Active", "Armor", "SpellDamage"], "sell": "2275"}, {"itemId": "3158", "name": "明朗之靴", "price": "350", "total": "900", "description": "<mainText><stats><attention>10</attention>技能急速<br><attention>45</attention>移动速度</stats><br><br><passive>艾欧尼亚的洞悉</passive><br>获得10召唤师技能急速。<br><br></mainText>", "item_desc": "10技能急速\n45移动速度 \n艾欧尼亚的洞悉\n获得10召唤师技能急速\n“这个物品是为了庆祝在联盟历20年12月10日艾欧尼亚和诺克萨斯的重赛中，艾欧尼亚取得胜利。\"", "into": ["3171"], "from": ["1001", "2022"], "types": ["Boots", "CooldownReduction"], "sell": "630"}, {"itemId": "3161", "name": "朔极之矛", "price": "675", "total": "3100", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>450</attention>生命值</stats><br><br><passive>龙之力量</passive><br>获得25基础技能急速。<br><br><passive>专注意志</passive><br>使用技能造成伤害会使你的英雄技能和被动伤害提升3%，持续6秒。（叠加4次）。</mainText>", "item_desc": "45攻击力\n450生命值 \n龙之力量\n获得25基础技能急速。\n专注意志\n在用技能造成伤害时，会使你的英雄的技能和被动伤害提升3%，持续6秒(可叠加4次)。\n【专注意志】已对英雄造成的额外伤害：未知\n【专注意志】的叠层在每段技能施放都有1秒停摆时间。", "into": [], "from": ["1028", "1037", "2021"], "types": ["AbilityHaste", "Damage", "Health"], "sell": "2170"}, {"itemId": "3165", "name": "莫雷洛秘典", "price": "400", "total": "2850", "description": "<mainText><stats><attention>75</attention>法术强度<br><attention>350</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>重伤</passive><br>对敌方英雄造成<magicDamage>魔法伤害</magicDamage>时会施加持续3秒的<keyword>40%重伤</keyword>效果。</mainText>", "item_desc": "75法术强度\n350生命值 \n15技能急速\n重伤\n对敌方英雄造成魔法伤害时会施加持续3秒的40%重伤效果。\n重伤:降低治疗效果和生命回复的效能。", "into": [], "from": ["1026", "3067", "3916"], "types": ["AbilityHaste", "CooldownReduction", "Health", "SpellDamage"], "sell": "1995"}, {"itemId": "3170", "name": "迅速进军", "price": "500", "total": "1500", "description": "<mainText><stats><attention>65</attention>移动速度</stats><br><br><passive>迅捷步</passive><br>将<keyword>减速</keyword>效果的效能降低25%。<br><br><passive>诺克萨斯的狂热</passive><br>获得相当于你5%<speed>移动速度</speed>的适应之力。 </mainText>", "item_desc": "65移动速度\n迅捷步\n将减速效果的效能降低25%。\n诺克萨斯的狂热\n获得相当于你5%移动速度的适应之力。\n“看我如何走位！”-德莱文", "into": [], "from": ["3009"], "types": ["Boots"], "sell": "1050"}, {"itemId": "3171", "name": "猩红明朗", "price": "500", "total": "1400", "description": "<mainText><stats><attention>25</attention>技能急速<br><attention>50</attention>移动速度</stats><br><br><passive>艾欧尼亚的洞悉</passive><br>获得10召唤师技能急速。<br><br><passive>诺克萨斯的急速</passive><br>通过技能对友军进行强化或保护时、通过技能对敌方英雄造成伤害时、或者施放一个召唤师技能时，会获得持续4秒的<speed>0移动速度</speed>。<br><br><rules>【诺克萨斯的急速】在每次技能施放仅会触发一次。</rules></mainText>", "item_desc": "25技能急速\n50移动速度\n艾欧尼亚的洞悉\n获得10召唤师技能急速\n诺克萨斯的急速\n【近战】通过技能对友军进行强化或保护时、通过技能对敌方英雄造成伤害时、或者施放一个召唤师技能时，会获得持续4秒的10%移动速度\n【远程】通过技能对友军进行强化或保护时、通过技能对敌方英雄造成伤害时、或者施放一个召唤师技能时，会获得持续4秒的8%移动速度", "into": [], "from": ["3158"], "types": ["Boots", "CooldownReduction"], "sell": "980"}, {"itemId": "3172", "name": "炮铜胫甲", "price": "500", "total": "1600", "description": "<mainText><stats><attention>40%</attention>攻击速度<br><attention>50</attention>移动速度</stats><br><br><passive>诺克萨斯的步法</passive><br>对英雄的普攻会提供在2秒内持续衰减的<speed>移动速度</speed> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "40%攻击速度\n50移动速度\n诺克萨斯的步法\n【近战】对英雄的普攻会提供在2秒内持续衰减的15%移动速度 攻击特效。\n【远程】对英雄的普攻会提供在2秒内持续衰减的10%移动速度 攻击特效。", "into": [], "from": ["3006"], "types": ["AttackSpeed", "NonbootsMovement"], "sell": "1120"}, {"itemId": "3173", "name": "带链碾碎者", "price": "500", "total": "1750", "description": "<mainText><stats><attention>35</attention>魔法抗性<br><attention>50</attention>移动速度<br><attention>30%</attention>韧性</stats><br><br><passive>诺克萨斯的不懈</passive> (0秒)<br>在受到来自英雄的魔法伤害后，获得一个持续4秒的<shield>0</shield><magicDamage>魔法</magicDamage><shield>护盾</shield>。</mainText>", "item_desc": "35魔法抗性\n50移动速度\n30%韧性\n诺克萨斯的不懈 (12秒)\n在受到来自英雄的魔法伤害后，获得一个持续4秒的(10-140【随等级提升】+4%【生命值】)魔法护盾", "into": [], "from": ["3111"], "types": ["Boots", "MagicResist", "SpellBlock", "Tenacity"], "sell": "1225"}, {"itemId": "3174", "name": "装甲战靴", "price": "500", "total": "1700", "description": "<mainText><stats><attention>40</attention>护甲<br><attention>50</attention>移动速度</stats><br><br><passive>镀板</passive><br>使即将到来的攻击伤害降低10%。<br><br><passive>诺克萨斯的耐久</passive>  (0秒)<br>在受到来自英雄的物理伤害后，获得一个持续4秒的<shield>0</shield><physicalDamage>物理</physicalDamage><shield>护盾</shield>。</mainText>", "item_desc": "40护甲\n50移动速度\n镀板\n使即将到来的攻击伤害降低12%。\n诺克萨斯的耐久(12秒)\n在受到来自英雄的物理伤害后，获得一个持续4秒的(10-140【随等级提升】+4%【生命值】)物理护盾。", "into": [], "from": ["3047"], "types": ["Armor", "Boots"], "sell": "1190"}, {"itemId": "3175", "name": "灵能使之靴", "price": "500", "total": "1600", "description": "<mainText><stats><attention>18</attention>法术穿透<br><attention>7%</attention>法术穿透<br><attention>50</attention>移动速度</stats><br><br></mainText>", "item_desc": "18法术穿透\n7%法术穿透\n50移动速度\n一记精心计算的步伐。无穷无尽的误导。", "into": [], "from": ["3020"], "types": ["Boots", "MagicPenetration"], "sell": "1120"}, {"itemId": "3176", "name": "永远前进", "price": "500", "total": "1400", "description": "<mainText><stats><attention>55</attention>移动速度</stats><br><br><passive>虚空生物</passive><br>获得【强化回城】。<br><br><passive>诺克萨斯的灵犀</passive><br>获得<speed>45移动速度</speed>和非战斗状态下的<speed>8%总移动速度</speed>。</mainText>", "item_desc": "55移动速度\n虚空生物\n获得【强化回城】。\n诺克萨斯的灵犀\n获得45移动速度和非战斗状态下的8%总移动速度。\n“诺克萨斯帝国将会永存……日臻完善。”", "into": [], "from": ["3013"], "types": ["Boots"], "sell": "980"}, {"itemId": "3177", "name": "守护者之刃", "price": "950", "total": "950", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>150</attention>生命值<br><attention>15</attention>技能急速</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Damage", "Health", "Lane"], "sell": "665"}, {"itemId": "3179", "name": "黯影阔剑", "price": "450", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>15</attention>穿甲<br><attention>10</attention>技能急速</stats><br><br><passive>封锁</passive><br>在你处在敌方侦察守卫和陷阱附近时，将其显形8秒。<br><br><passive>熄灭</passive><br>攻击对守卫造成额外伤害。</mainText>", "item_desc": "55攻击力\n15穿甲\n10技能急速 \n封锁(90秒)\n在你处在敌方侦察守卫和陷阱附近时，将其显形8秒。\n熄灭\n攻击对守卫造成【近战】3伤害\n攻击对守卫造成【远程】2伤害", "into": [], "from": ["3133", "3134"], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage", "Vision"], "sell": "1750"}, {"itemId": "3181", "name": "破舰者", "price": "175", "total": "3000", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>500</attention>生命值<br><attention>4%</attention>移动速度</stats><br><br><passive>船长</passive><br>对英雄和史诗级野怪的每第五次攻击造成<physicalDamage>额外物理伤害</physicalDamage>，对建筑物造成更多伤害。<br><br><passive>登舰小组</passive><br>附近的友军攻城兵和超级士兵获得<scaleArmor>护甲</scaleArmor>和<scaleMR>魔法抗性</scaleMR>。</mainText>", "item_desc": "40攻击力\n500生命值\n4%移动速度 \n船长\n【近战】对英雄和史诗级野怪的每第五次攻击造成【近程】(120% 基础 【攻击力】+5%【生命值】)额外物理伤害，对建筑物提升至(300% 基础【攻击力】+10%【生命值】)。\n【远程】对英雄和史诗级野怪的每第五次攻击造成【远程】(84% 基础 【攻击力】+3.5%【生命值】)额外物理伤害，对建筑物提升至(210% 基础【攻击力】+7%【生命值】)。\n登舰小组\n附近的友军攻城兵和超级士兵获得了近战(70-130【等级提升】)护甲和魔法抗性\n附近的友军攻城兵和超级士兵获得了远程(35-65【等级提升】)护甲和魔法抗性", "into": [], "from": ["1037", "2021", "3066"], "types": ["Damage", "Health", "NonbootsMovement"], "sell": "2100"}, {"itemId": "3184", "name": "守护者战锤", "price": "950", "total": "950", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>150</attention>生命值<br><attention>5%</attention>生命偷取</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health", "Lane", "LifeSteal"], "sell": "665"}, {"itemId": "3190", "name": "钢铁烈阳之匣", "price": "700", "total": "2200", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>25</attention>护甲<br><attention>25</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br> <active>虔诚</active><br>为附近友军提供<shield>200 - 360护盾值</shield>，在2.5秒里持续衰减。</mainText>", "item_desc": "200生命值\n25护甲\n25魔法抗性\n10技能急速 \n虔诚(90秒)\n为附近友军提供200-360(友军【等级提升】)护盾值，在2.5秒里持续衰减。\n20秒内的后续虔诚护盾拥有25%效果。", "into": [], "from": ["1029", "1033", "3067"], "types": ["AbilityHaste", "Active", "Armor", "<PERSON>ra", "Health", "MagicResist", "SpellBlock"], "sell": "1540"}, {"itemId": "3211", "name": "幽魂斗篷", "price": "150", "total": "1250", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>35</attention>魔法抗性<br><attention>100%</attention>基础生命回复</stats><br><br></mainText>", "item_desc": "200生命值\n35魔法抗性\n100%基础生命回复", "into": ["2504", "3065", "6664"], "from": ["1006", "1028", "1033"], "types": ["Health", "HealthRegen", "SpellBlock"], "sell": "875"}, {"itemId": "3222", "name": "米凯尔的祝福", "price": "900", "total": "2300", "description": "<mainText><stats><attention>250</attention>生命值<br><attention>100%</attention>基础法力回复<br><attention> 12%</attention>治疗和护盾强度<br><attention>15</attention>技能急速</stats><br><br> <active>纯化</active> <br>移除一名友方英雄身上的所有控制类减益效果(<keyword>滞空</keyword>和<keyword>压制</keyword>除外)并为其回复<healing>100 - 250生命值</healing>。</mainText>", "item_desc": "250生命值\n100%基础法力回复\n12%治疗和护盾强度\n15技能急速 \n纯化(120秒)\n移除一名友方英雄身上的所有控制类减益效果(滞空和压制除外)并为其回复100-250(友军【等级提升】)生命值。", "into": [], "from": ["3067", "3114"], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health", "ManaRegen", "Tenacity"], "sell": "1610"}, {"itemId": "3302", "name": "界弓", "price": "1100", "total": "3000", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>35%</attention>攻击速度</stats><br><br><passive>晦影</passive><br>攻击造成<magicDamage>30额外魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。<br><br><passive>交相</passive><br>对抗英雄时，在<keywordMajor>光明</keywordMajor>特效和<keywordMajor>黑暗</keywordMajor>特效之间轮换：<li>附带<keywordMajor>光明</keywordMajor>特效的攻击提供持续5秒的<scaleArmor>护甲</scaleArmor>和<scaleMR>魔抗</scaleMR>。<li>附带<keywordMajor>黑暗</keywordMajor>特效的攻击提供持续5秒的10%<scaleArmor>护甲穿透</scaleArmor>和<scaleMR>法术穿透</scaleMR>。 </mainText>", "item_desc": "30攻击力\n35%攻击速度 \n晦影\n攻击造成30额外魔法伤害攻击特效。\n交相\n对抗英雄时，在光明特效和黑暗特效之间轮换:\n附带光明特效的攻击提供持续5秒的(6-8【等级提升】)护甲和魔抗(至多至(18-24)【等级提升】)。\n附带黑暗特效的攻击提供持续5秒的10%护甲穿透和法术穿透(至多至30%)。", "into": [], "from": ["1043", "3051"], "types": ["ArmorPenetration", "AttackSpeed", "Damage", "MagicPenetration", "OnHit"], "sell": "2100"}, {"itemId": "3330", "name": "草间人", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>不能售出<br><br><active>主动 - 饰品：</active>放置一个持续0秒的草间人，并且现身于敌人视野时会对敌人做出和费德提克一样的动作。每0秒储存1层充能，至多可储存2层充能。<br><br>敌方英雄在接近草间人时会将其激活，使该草间人假装进行一个随机的动作，然后该草间人会四分五裂。</mainText>", "item_desc": "不能售出\n主动-饰品:放置一个持续(130-300【等级提升】)秒的草间人，并且现身于敌人视野时会\n对敌人做出和费德提克一样的动作。每(115-30【等级提升】)秒储存1层充能，至多可储存2层\n充能。\n敌方英雄在接近草间人时会将其激活，使该草间人假装进行一个随机的动作，然后该草间人\n会四分五裂。", "into": [], "from": [], "types": ["Active", "Jungle", "Lane", "Trinket", "Vision"], "sell": "0"}, {"itemId": "3340", "name": "侦查守卫", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br> <active>主动</active>  (<scaleLevel>210 - 120</scaleLevel>秒，最大2层充能)<br>放置一个<keyword>隐形</keyword>的侦察守卫来提供视野，持续<scaleLevel>90 - 120</scaleLevel>秒。</mainText>", "item_desc": "主动(210-120秒，最大2层充能)\n放置一个隐形的侦察守卫来提供视野，持续90-120秒。", "into": [], "from": [], "types": ["Active", "Jungle", "Lane", "Trinket", "Vision"], "sell": "0"}, {"itemId": "3363", "name": "远见改造", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br> <active>主动</active>  (<scaleLevel>198 - 99</scaleLevel>秒)<br>显形一个远方区域2秒并留下一个守卫，这个守卫会在侦测到一个敌方英雄后到期。</mainText>", "item_desc": "需要达到等级9\n主动(198-99秒)\n显形一个远方区域2秒并留下一个守卫，这个守卫会在侦测到一个敌方英雄后到期。\n可以看到地形和草丛的内部。\n友方无法以这个守卫为目标。", "into": [], "from": [], "types": ["Active", "Trinket", "Vision"], "sell": "0"}, {"itemId": "3364", "name": "神谕透镜", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br> <active>主动</active>  (<scaleLevel>160 - 100</scaleLevel>秒，最大2层充能)<br>显形你周围的敌方侦察守卫和陷阱，持续6秒。</mainText>", "item_desc": "主动(160-100秒，最大2层充能)\n显形你周围的敌方侦察守卫和陷阱，持续6秒。\n在附近有隐形单位时会提醒你。\n被显形的侦察守卫会在显形期间失效。", "into": [], "from": [], "types": ["Active", "Trinket", "Vision"], "sell": "0"}, {"itemId": "3430", "name": "毁坏仪式", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>法术强度<br><attention>15</attention>技能急速<br><attention>25%</attention>暴击几率</stats><br><br><passive>怒火与破坏</passive><br>在施放技能时，获得持续6秒的 2.5%暴击几率，至多可叠至 20%。<br><br><passive>打捞残骸</passive><br>你的技能有几率(与你的暴击几率相同)给你或你的目标友军提供持续3秒的0<healing>护盾值</healing>。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["CooldownReduction", "CriticalStrike", "SpellDamage"], "sell": "1250"}, {"itemId": "3504", "name": "炽热香炉", "price": "700", "total": "2200", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention> 10%</attention>治疗和护盾强度<br><attention>125%</attention>基础法力回复<br><attention>4%</attention>移动速度</stats><br><br><passive>圣洁化</passive><br>对一名友方英雄施加治疗或护盾时，会使你们两个都获得持续6秒的强化，提供<attackSpeed>25%攻击速度</attackSpeed>和<magicDamage>20魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "45法术强度\n10%治疗和护盾强度\n125%基础法力回复\n4%移动速度 \n圣洁化\n对一名友方英雄施加治疗或护盾时，会使你们两个都获得持续6秒的强化，提供25%攻击速度和20魔法伤害攻击特效。", "into": [], "from": ["3113", "3114"], "types": ["AttackSpeed", "ManaRegen", "NonbootsMovement", "SpellDamage"], "sell": "1540"}, {"itemId": "3508", "name": "夺萃之镰", "price": "375", "total": "2900", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>15</attention>技能急速<br><attention>25%</attention>暴击几率</stats><br><br><passive>精萃汲取</passive><br>攻击提供<scaleMana>法力值</scaleMana><OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "60攻击力\n15技能急速\n25%暴击几率 \n\n精萃汲取\n攻击提供(15 +10% 【额外攻击力】)法力值攻击特效。", "into": [], "from": ["1018", "1037", "3133"], "types": ["AbilityHaste", "CooldownReduction", "CriticalStrike", "Damage", "ManaRegen", "OnHit"], "sell": "2030"}, {"itemId": "3599", "name": "卡莉丝塔的黑色长矛", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active>选择一位友军进行绑定，让该友军在该局的剩余时间里成为誓约者。誓约者在你附近时，你和誓约者彼此都会得到强化。</mainText>", "item_desc": "主动-消耗:选择一位友军进行绑定，让该友军在该局的剩余时间里成为誓约者。\n誓约者在你附近时，你和誓约者彼此都会得到强化。", "into": [], "from": [], "types": ["Consumable"], "sell": "0"}, {"itemId": "3600", "name": "卡莉丝塔的黑色长矛", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active>选择一位友军进行绑定，让该友军在该局的剩余时间里成为誓约者。誓约者在你附近时，你和誓约者彼此都会得到强化。<br><br><rules>需要使用<attention>卡莉丝塔</attention>的终极技能。</rules></mainText>", "item_desc": "主动-消耗:选择一位友军进行绑定，让\n该友军在该局的剩余时间里成为誓约者。誓约\n者在你附近时，你和誓约者彼此都会得到强\n化。", "into": [], "from": [], "types": ["Consumable"], "sell": "0"}, {"itemId": "3742", "name": "亡者的板甲", "price": "900", "total": "2900", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>55</attention>护甲<br><attention>4%</attention>移动速度</stats><br><br><passive>沉船者</passive><br>在移动时，积攒至多<speed>20额外移动速度</speed>。你的下一次攻击会释放掉已积攒的移动速度来造成<physicalDamage>额外物理伤害</physicalDamage>。<br><br><passive>永不沉没</passive><br>使所受的<keyword>减速</keyword>效果的效能降低15%。</mainText>", "item_desc": "350生命值\n55护甲\n4%移动速度 \n沉船者\n在移动时，积攒至多20额外移动速度。你的下一次攻击会释放掉已积攒的移动速度来至多造成(100% 基础【攻击力】+40)物理伤害。\n永不沉没\n所受的移动减速效果的强度降低15%。\n\"只有一种方法能让你从我文里拿到这件盔田……-被遗忘的名字", "into": [], "from": ["1028", "1031", "3066"], "types": ["Armor", "Health", "NonbootsMovement", "Slow"], "sell": "2030"}, {"itemId": "3748", "name": "巨型九头蛇", "price": "50", "total": "3300", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>600</attention>生命值</stats><br><br><passive>顺劈</passive><br>攻击附带<physicalDamage>物理伤害</physicalDamage>并对目标身后的敌人们造成物理伤害。<br> <active>刚斩</active><br>强化你的下一次<passive>顺劈</passive>，使其造成<physicalDamage>额外物理伤害</physicalDamage> <OnHit>攻击特效</OnHit>并对目标身后的敌人们造成<physicalDamage>额外物理伤害</physicalDamage>。</mainText>", "item_desc": "40攻击力\n600生命值 \n\n顺劈\n【近战】攻击附带(1%【生命值】)物理伤害并对目标身后的敌人们造成(3%【生命值】)物理伤害。\n【远程】攻击附带(0.5%【生命值】)物理伤害 必攻击特效并对目标身后的敌人们造成了(1.5%【生命值】)物理伤害。\n\n刚斩(10秒)\n【近战】强化你的下一次顺劈，使其造成(4%【生命值】)额外物理伤害并对目标身后的敌人们造成(9%【生命值】)颜外物理伤害。\n【远程】强化你的下一次顺劈，使其造成(2%【生命值】)额外物理伤害并对目标身后的敌人们造成(4.5%【生命值】)额外物理伤害\n\n攻击特效伤害也可作用于建筑物，\n顺劈的冲击波不会在建筑物上触发。", "into": [], "from": ["1011", "2021", "3077"], "types": ["Damage", "Health", "HealthRegen", "OnHit"], "sell": "2310"}, {"itemId": "3801", "name": "晶体护腕", "price": "100", "total": "800", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>100%</attention>基础生命回复</stats><br><br></mainText>", "item_desc": "200生命值\n100%基础生命回复", "into": ["3083", "3084"], "from": ["1006", "1028"], "types": ["Health", "HealthRegen"], "sell": "560"}, {"itemId": "3802", "name": "遗失的章节", "price": "250", "total": "1200", "description": "<mainText><stats><attention>40</attention>法术强度<br><attention> 300</attention>法力<br><attention>10</attention>技能急速</stats><br><br><passive>启蒙</passive><br>升级时会在3秒里持续回复共<scaleMana>20%最大法力值</scaleMana>。</mainText>", "item_desc": "40法术强度\n300法力\n10技能急速 \n\n启蒙\n升级时会在3秒里持续回复共20%最大法力值", "into": ["2503", "3003", "3118", "4644", "6655", "6656", "323003"], "from": ["1027", "1052", "2022"], "types": ["AbilityHaste", "CooldownReduction", "<PERSON><PERSON>", "ManaRegen", "SpellDamage"], "sell": "840"}, {"itemId": "3803", "name": "万世催化石", "price": "200", "total": "1300", "description": "<mainText><stats><attention>350</attention>生命值<br><attention> 375</attention>法力</stats><br><br><passive>永恒</passive><br>将来自英雄的<scaleMana>10%所受伤害</scaleMana>回复为<scaleMana>法力值</scaleMana>。<br>施放一个技能时，<healing>治疗自身相当于25%法力</healing>消耗的生命值。</mainText>", "item_desc": "350生命值\n375法力 \n\n永恒\n承受来自英雄的10%所受伤害回复为法力值。\n施放一个技能时，治疗自身相当于25%法力消耗的生命值。\n每次技能施放或每秒技能维持的治疗上限为20。", "into": ["4402", "6657", "326657"], "from": ["1027", "1028", "1028"], "types": ["Health", "HealthRegen", "<PERSON><PERSON>", "ManaRegen"], "sell": "910"}, {"itemId": "3814", "name": "夜之锋刃", "price": "850", "total": "3000", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>15</attention>穿甲<br><attention>250</attention>生命值</stats><br><br><passive>废除</passive><br>提供一层法术护盾来格挡下一个敌方技能。</mainText>", "item_desc": "50攻击力\n15穿甲\n250生命值 \n\n废除(40秒)\n提供一层法术护盾来格挡下一个敌方技能。\n如果在此装备冷却完毕之前受到来自英雄的伤害，那么它的冷却时间会重新开始计算。", "into": [], "from": ["2021", "3134"], "types": ["ArmorPenetration", "Damage", "Health"], "sell": "2100"}, {"itemId": "3865", "name": "云游图鉴", "price": "400", "total": "400", "description": "", "item_desc": "30生命值\n25%基础生命回复\n25%基础法力回复\n3金币/10秒 \n\n辅助任务\n使用这个装备赚取400金币来将其转变为符文罗盘。\n分享财富(20秒，最大3层充能)\n\n在一名友方英雄附近时，对敌方英雄造成伤害或者击杀小兵即可获得金币。\n- 对英雄或建筑物进行的伤害型技能和攻击会提供(22)金币;\n- 以任意方式击杀一个小兵会为你提供15金币并为相距最近的友方英雄提供击杀金币，\n在已方任一成员用这件装备击杀了过多数量的小兵时，这件装备会降低来自小兵的金币。\n“去找，就终会找到。", "into": [], "from": [], "types": ["<PERSON><PERSON><PERSON>", "Health", "Lane", "ManaRegen", "Vision"], "sell": "160"}, {"itemId": "3869", "name": "星界据守", "price": "0", "total": "400", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>75%</attention>基础生命回复<br><attention>75%</attention>基础法力回复<br><attention>5</attention>金币/10秒</stats><br><br><passive>神峰庇佑</passive><br>使所承受的来自英雄的伤害降低，受到来自英雄的伤害后会存留2秒。当这个效果结束时，对附近敌人造成持续1.5秒的50%减速。<br> <active>主动</active> (4层充能)<br>放置一个<keyword>隐形</keyword>的侦察守卫来提供视野。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n200生命值\n75%基础生命回复\n75%基础法力回复\n5金币/10秒 \n\n神峰庇佑(18秒)\n使所承受的来自英雄的伤害降低近战35%/远程25%，受到来自英雄的伤害后会存留2秒。当这个效果结束时，对附近敌人造成持续1.5秒的50%减速。\n\n主动(4层充能)\n放置一个隐形的侦察守卫来提供视野。\n在访问商店时重新补满充能。\n在已方任一成员用这件装备击杀了过多数量的小兵时，这件装备会降低来自小兵的金币。\n如果在此装备冷却完毕之前受到来自英雄的伤害，那么它的冷却时间会重新开始计算。\n献给那些敢于攀登的人。", "into": [], "from": ["3867"], "types": ["<PERSON><PERSON><PERSON>", "Health", "HealthRegen", "Lane", "ManaRegen", "Vision"], "sell": "160"}, {"itemId": "3870", "name": "圆梦使者", "price": "0", "total": "400", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>75%</attention>基础生命回复<br><attention>75%</attention>基础法力回复<br><attention>5</attention>金币/10秒</stats><br><br><passive>圆梦使者</passive><br>为另一个友军提供治疗或护盾时，美梦泡泡会飞向目标队友，持续3秒。该友军的下一次攻击造成<magicDamage>额外魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>并且所受的下一段伤害降低。 <active>主动</active> (4层充能)<br>放置一个<keyword>隐形</keyword>的侦察守卫来提供视野。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n200生命值\n75%基础生命回复\n75%基础法力回复\n5金币/10秒 \n\n圆梦使者(8秒)\n为另一个友军提供治疗或护盾时，美梦泡泡会飞向目标队友，持续3秒。该友军的下一次攻击造成(50-170【等级提升】)额外魔法伤害并且其所受的下一段伤害降低(75-255【等级提升】)。\n\n主动(4层充能)\n放置一个隐形的侦察守卫来提供视野。\n在访问商店时重新补满充能。\n在已方任一成员用这件装备击杀了过多数量的小兵时，这件装备会降低来自小兵的金币。\n你的班德尔城助眠小伙伴。", "into": [], "from": ["3867"], "types": ["<PERSON><PERSON><PERSON>", "Health", "HealthRegen", "Lane", "ManaRegen", "Vision"], "sell": "160"}, {"itemId": "3871", "name": "扎兹沙克的溃口", "price": "0", "total": "400", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>75%</attention>基础生命回复<br><attention>75%</attention>基础法力回复<br><attention>5</attention>金币/10秒</stats><br><br><passive>虚空扩爆</passive><br>对英雄造成技能伤害会引发一次爆炸，造成<magicDamage>魔法伤害</magicDamage>。<br><br> <active>主动</active> (4层充能)<br>放置一个<keyword>隐形</keyword>的侦察守卫来提供视野。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n200生命值\n75%基础生命回复\n75%基础法力回复\n5金币/10秒 \n\n虚空扩爆(10)秒\n对英雄造成技能伤害会引发一次爆炸，造成(10 +15%【法术强度】)+3%最大生命值的魔法伤害。\n\n主动(4层充能)\n放置一个隐形的侦察守卫来提供视野。\n在访问商店时重新补满充能。\n在己方任一成员用这件装备击杀了过多数量的小兵时，这件装备会降低来自小兵的金币。\n对野怪的伤害封顶值为300。\n只有非常勇敢或非常愚嘉的人才敢让虚空与自己为敌。幸运的是，扎兹沙克二者兼有。", "into": [], "from": ["3867"], "types": ["<PERSON><PERSON><PERSON>", "Health", "HealthRegen", "Lane", "ManaRegen", "Vision"], "sell": "160"}, {"itemId": "3876", "name": "摩天雪橇", "price": "0", "total": "400", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>75%</attention>基础生命回复<br><attention>75%</attention>基础法力回复<br><attention>5</attention>金币/10秒</stats><br><br><passive>雪橇出击</passive><br><keyword>减速</keyword>或<keyword>定身</keyword>一名处在友军附近的敌方英雄时，会为你和附近的一名友军回复<healing>生命值</healing>并提供在2.5秒里持续衰减的<speed>20%移动速度</speed>。  <active>主动</active> (4层充能)<br>放置一个<keyword>隐形</keyword>的侦察守卫来提供视野。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n200生命值\n75%基础生命回复\n75%基础法力回复\n5金币/10秒 \n\n雪撬出击(30秒)\n减速或定身一名处在友军附近的地方英雄时，会为你和附近的一名友军回复(50-230【等级提升】)生命值并提供在2.5秒里持续衰减的20%移动速度。\n\n主动(4层充能)\n放置一个隐形的侦察守卫来提供视野。\n在访问商店时重新补满充能。\n在己方任一成员用这件装备击杀了过多数量的小兵时，这件装备会降低来自小兵的金币。\n“来吧，小伙伴--咱们去滑雪撬!\"\n减速:移动速度被降低了。\n定身:无法控制移动。", "into": [], "from": ["3867"], "types": ["<PERSON><PERSON><PERSON>", "Health", "HealthRegen", "Lane", "ManaRegen", "Vision"], "sell": "160"}, {"itemId": "3877", "name": "血鸣", "price": "0", "total": "400", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>75%</attention>基础生命回复<br><attention>75%</attention>基础法力回复<br><attention>5</attention>金币/10秒</stats><br><br><passive>咒刃</passive><br>施放技能后，你的下一次普通攻击会造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>。如果目标是英雄，则使其承受的伤害提升，持续4秒。 <active>主动</active> (4层充能)<br>放置一个<keyword>隐形</keyword>的侦察守卫来提供视野。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n200生命值\n75%基础生命回复\n75%基础法力回复\n5金币/10秒 \n\n咒刃(1.5秒)\n施放技能后，你的下一次普通攻击会造成(100%基础【攻击力】 )物理伤害。\n如果目标是英雄，则使其承受的伤害提升近战8%/远程5%，持续4秒。\n\n主动(4层充能)\n放置一个隐形的侦察守卫来提供视野。\n并在访问商店时重新补满充能。\n在己方任一成员用这件装备击杀了过多数量的小兵时，这件装备会降低来自小兵的金币。\n“啊，万众怒吼……血染黄沙。“", "into": [], "from": ["3867"], "types": ["<PERSON><PERSON><PERSON>", "Health", "HealthRegen", "Lane", "ManaRegen", "Vision"], "sell": "160"}, {"itemId": "3916", "name": "湮灭宝珠", "price": "400", "total": "800", "description": "<mainText><stats><attention>25</attention>法术强度</stats><br><br><passive>重伤</passive><br>对敌方英雄造成<magicDamage>魔法伤害</magicDamage>时会施加持续3秒的<keyword>40%重伤</keyword>效果。</mainText>", "item_desc": "25法术强度 \n重伤\n对英雄造成魔法伤害时会施加持续3秒的40%重伤效果\n重伤:降低治疗效果和生命回复的效能，", "into": ["3011", "3165", "223011"], "from": ["1052"], "types": ["SpellDamage"], "sell": "560"}, {"itemId": "4003", "name": "救生索", "price": "475", "total": "1600", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>5</attention>穿甲<br><attention>4%</attention>移动速度</stats><br><br><active>主动 - </active> <active>灵魂之锚</active>  (0秒)<br>标记你的当前位置。在4秒后，回到被标记的位置。 你可以在<active>灵魂之锚</active>期间的任一时间点进行重新施放以提前回到你标记的位置。</mainText>", "item_desc": "", "into": ["4004"], "from": ["1036", "6690"], "types": ["ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "1120"}, {"itemId": "4004", "name": "幽魂弯刀", "price": "400", "total": "3000", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>15</attention>穿甲<br><attention>4%</attention>移动速度</stats><br><br><active>主动 - </active> <active>灵魂之锚</active>  (0秒)<br>标记你的当前位置。在4秒后，回到被标记的位置。 你可以在<active>灵魂之锚</active>期间的任一时间点进行重新施放以提前回到你标记的位置。</mainText>", "item_desc": "", "into": [], "from": ["3134", "4003"], "types": ["ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "2100"}, {"itemId": "4005", "name": "帝国指令", "price": "500", "total": "2250", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>20</attention>技能急速<br><attention>125%</attention>基础法力回复</stats><br><br><passive>协同开火</passive> 每个目标 (0秒)<br><keyword>减速</keyword>或<keyword>定身</keyword>一名英雄时会将其标记5秒。友方英雄的伤害会引爆标记，造成<magicDamage>相当于10%当前生命值的魔法伤害</magicDamage>。 </mainText>", "item_desc": "60法术强度\n20技能急速\n125%基础法力回复 \n\n协同开火 每个目标(9秒)\n减速或定身一名英雄时会将其标记5秒。友方英雄的伤害会引爆标记，造成相当于10%当前生命值的魔法伤害。\n减速:移动速度被降低了。\n定身:无法控制移动。", "into": [], "from": ["3108", "4642"], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "1575"}, {"itemId": "4010", "name": "放血者的诅咒", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>350</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>恶劣衰朽</passive><br>用技能或被动对英雄造成<magicDamage>魔法伤害</magicDamage>时，会对其造成持续6秒的<scaleMR>5%魔法抗性削减</scaleMR>，至多可叠至30%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CooldownReduction", "MagicPenetration", "SpellBlock", "SpellDamage"], "sell": "1250"}, {"itemId": "4011", "name": "花晓之剑", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention>200</attention>生命值<br><attention> 12%</attention>治疗和护盾强度<br><attention>15</attention>技能急速</stats><br><br><passive>欢腾</passive><br>你每拥有<healing>1%治疗和护盾强度</healing>，就会获得+ 1.2%攻击速度。<br><br><passive>薄荷</passive><br><OnHit>攻击特效</OnHit>，<healing>治疗你附近生命值最低</healing>的那个友方英雄0生命值，优先治疗更低生命值的友军。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "OnHit", "SpellDamage"], "sell": "1250"}, {"itemId": "4012", "name": "食罪者", "price": "3000", "total": "3000", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>45</attention>护甲<br><attention>45</attention>魔法抗性<br><attention>30%</attention>韧性</stats><br><br><br><li><passive>盛宴：</passive>每当一个附近的友方英雄受到一个定身类控制效果的影响时，你会转而晕眩，时长相当于该控制效果的时长(20 (0秒))。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Health", "SpellBlock", "Tenacity"], "sell": "2100"}, {"itemId": "4013", "name": "闪电穗带", "price": "3000", "total": "3000", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention>30%</attention>韧性</stats><br><br><br><li><passive>连锁闪电：</passive>你造成的技能伤害降低20%。受到过你的技能伤害的那些敌人，每过1秒就会将你对其造成的66.6%技能伤害连锁给一名附近的敌人，优先选择英雄。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "SpellBlock", "SpellDamage", "Tenacity"], "sell": "2100"}, {"itemId": "4015", "name": "困惑", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>5%</attention>移动速度<br><attention>22%</attention>护甲穿透<br><attention>30%</attention>法术穿透</stats><br><br><passive>巨人杀手</passive><br>对最大生命值高于你的敌方英雄至多造成15%额外伤害。<br><br><rules>当最大生命值差异大于2500时，即可达到最大伤害值提升。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "MagicPenetration", "NonbootsMovement", "SpellDamage"], "sell": "1250"}, {"itemId": "4016", "name": "无言承诺", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>法术强度<br><attention> 25%</attention>治疗和护盾强度<br><attention>25</attention>技能急速</stats><br><br><passive>承诺</passive><br>获得被你<attention>承诺</attention>的<scaleAP>友军的20%  法术强度</scaleAP>，并为被你<attention>承诺</attention>的友军提供你的20%  技能急速。<br><br> <active>主动</active>  (0秒)<br><active>承诺</active><br>对一名友军做出<attention>承诺</attention>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "SpellDamage"], "sell": "1250"}, {"itemId": "4017", "name": "恶火小斧", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>35</attention>攻击力<br><attention>12</attention>穿甲</stats><br><br><passive>烧焦</passive>  (0秒)<br>你的下一个技能在命中时，会<keywordMajor>灼烧</keywordMajor>敌人，在4秒里持续造成<scaleAD>共0%到0%当前生命值的物理伤害</scaleAD>，基于目标超过你的<scaleHealth>最大生命值</scaleHealth>而提升。<br><br><rules>额外伤害的最大值会在2000生命值差异时达到</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage"], "sell": "1250"}, {"itemId": "4401", "name": "自然之力", "price": "750", "total": "2800", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>55</attention>魔法抗性<br><attention>4%</attention>移动速度</stats><br><br><passive>坚定</passive><br>受到8次来自英雄的魔法伤害后，获得<scaleMR>70魔法抗性</scaleMR>和<speed>6%额外移动速度</speed>。</mainText>", "item_desc": "400生命值\n55魔法抗性\n4%移动速度 \n\n坚定\n受到8次来自英雄的魔法伤害后，获得70魔法抗性和6%额外移动速度。\n\n定身效果视作2次伤害。\n坚定会在7秒后重置。", "into": [], "from": ["1028", "1057", "3066"], "types": ["Health", "NonbootsMovement", "SpellBlock"], "sell": "1960"}, {"itemId": "4402", "name": "激发之匣", "price": "600", "total": "2950", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>400</attention>生命值<br><attention> 300</attention>法力<br><attention>10</attention>技能急速</stats><br><br><br><li><passive>永恒：</passive>承受来自英雄的伤害时，你会回复相当于15%折前伤害值的法力值；花费法力时，你会回复相当于25%法力消耗的生命值，每次施法每秒最多治疗20生命值。<li><passive>激发：</passive>在使用一次技能后，在3秒里持续回复共<healing>3%已损失生命值</healing>和<scaleMana>3%已损失法力值</scaleMana>。<br><br><flavorText>“挫败那些诺克萨斯人” <br>- 神庙守卫鲍迪尔</flavorText></mainText>", "item_desc": "", "into": [], "from": ["3133", "3803"], "types": ["AbilityHaste", "CooldownReduction", "Damage", "Health", "<PERSON><PERSON>"], "sell": "2065"}, {"itemId": "4403", "name": "金铲铲", "price": "687", "total": "7187", "description": "<mainText><stats><attention>70</attention>攻击力<br><attention>120</attention>法术强度<br><attention>50%</attention>攻击速度<br><attention>30%</attention>暴击几率<br><attention>250</attention>生命值<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention> 250</attention>法力<br><attention>20</attention>技能急速<br><attention>10%</attention>移动速度<br><attention>10%</attention>生命偷取<br><attention>100%</attention>基础生命回复<br><attention>100%</attention>基础法力回复</stats><br><br><br><li><passive>正在搞事：</passive>你正处在永久的火力全开中！<br><br><flavorText>“它必须做点儿什么……”<br>“错了，它什么都做得到！”</flavorText></mainText>", "item_desc": "", "into": [], "from": ["1038", "1053", "1058", "3067", "3086", "3105"], "types": ["Armor", "AttackSpeed", "CooldownReduction", "CriticalStrike", "Damage", "Health", "HealthRegen", "LifeSteal", "<PERSON><PERSON>", "ManaRegen", "NonbootsMovement", "SpellBlock", "SpellDamage"], "sell": "5031"}, {"itemId": "4628", "name": "视界专注", "price": "250", "total": "2800", "description": "<mainText><stats><attention>115</attention>法术强度<br><attention>25</attention>技能急速</stats><br><br><passive>高能射击</passive><br>在对600码或更远的英雄造成技能伤害时，将其<keywordStealth>显形</keywordStealth>6秒。<br><br><passive>专注</passive><br>在<passive>高能射击</passive>触发时，<keywordStealth>显形</keywordStealth>目标1400码内的所有其它敌方英雄3秒。</mainText>", "item_desc": "115法术强度\n25技能急速 \n\n高能射击\n在对600码或更远的英雄造成技能伤害时，将其显形6秒。\n\n专注(30秒)\n在高能射击触发时，显形目标1400码内的所有其它敌方英雄3秒。\n\n来自宠物和陷阱的伤害不会触发高能射击。\n领域类技能只有在初始放置时会触发高能射击。\n距离是从技能施放位置处开始算的。", "into": [], "from": ["1026", "3108", "3108"], "types": ["AbilityHaste", "SpellDamage"], "sell": "1960"}, {"itemId": "4629", "name": "星界驱驰", "price": "450", "total": "3000", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention>350</attention>生命值<br><attention>25</attention>技能急速<br><attention>4%</attention>移动速度</stats><br><br><passive>咒舞</passive><br>对英雄造成<magicDamage>魔法伤害</magicDamage>或<trueDamage>真实伤害</trueDamage>时会提供<speed>移动速度</speed>，持续4秒。 </mainText>", "item_desc": "70法术强度\n350生命值\n25技能急速\n4%移动速度 \n\n咒舞\n对敌方英雄造成魔法伤害或真实伤害时会提供(20)移动速度，持续4秒。", "into": [], "from": ["3067", "3108", "3113"], "types": ["AbilityHaste", "Health", "NonbootsMovement", "SpellDamage"], "sell": "2100"}, {"itemId": "4630", "name": "枯萎珠宝", "price": "700", "total": "1100", "description": "<mainText><stats><attention>25</attention>法术强度<br><attention>13%</attention>法术穿透</stats><br><br></mainText>", "item_desc": "25法术强度\n13%法术穿透", "into": ["3135", "3137"], "from": ["1052"], "types": ["MagicPenetration", "SpellDamage"], "sell": "770"}, {"itemId": "4632", "name": "翠绿屏障", "price": "400", "total": "1600", "description": "<mainText><stats><attention>40</attention>法术强度<br><attention>25</attention>魔法抗性</stats><br><br><passive>废除</passive><br>提供一层法术护盾来格挡下一个敌方技能。</mainText>", "item_desc": "40法术强度\n25魔法抗性 \n\n废除(60秒)\n提供一层法术护盾来格挡下一个敌方技能。\n如果在此装备冷却完毕之前受到来自英雄的伤害，那么它的冷却时间会重新开始计算。", "into": ["3102"], "from": ["1033", "1052", "1052"], "types": ["SpellBlock", "SpellDamage"], "sell": "1120"}, {"itemId": "4633", "name": "裂隙制造者", "price": "950", "total": "3100", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention>350</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>虚空腐蚀</passive><br>在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至8%。在最大强度时，获得<omnivamp>全能吸血</omnivamp>。<br><br><passive>虚空灌注</passive><br>获得相当于你2%<scaleHealth>额外生命值</scaleHealth>的<scaleAP>法术强度</scaleAP>。</mainText>", "item_desc": "70法术强度\n350生命值\n15技能急速 \n\n虚空腐蚀\n【近战】在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至8%。在最大强度时，获得(10%)全能吸血。\n【远战】在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至8%。在最大强度时，获得(6%)全能吸血。\n\n虚空灌注\n获得相当于你2%额外生命值的法术强度。", "into": [], "from": ["3108", "3147"], "types": ["CooldownReduction", "Health", "SpellDamage", "SpellVamp"], "sell": "2170"}, {"itemId": "4638", "name": "戒备眼石", "price": "1100", "total": "1100", "description": "<mainText><stats><attention>150</attention>生命值<br><attention>10</attention>技能急速<br><attention>10</attention>护甲<br><attention>15</attention>魔法抗性</stats><br><br><passive>奥术窖藏</passive><br>这件装备至多可储存3个已购买的控制守卫。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n150生命值\n10技能急速\n10护甲\n15魔法抗性 \n\n奥术窖藏\n这件装备至多可储存3个已购买的控制守卫。", "into": ["4643"], "from": [], "types": ["AbilityHaste", "Active", "Armor", "CooldownReduction", "Health", "SpellBlock", "Vision"], "sell": "770"}, {"itemId": "4642", "name": "班德尔玻璃镜", "price": "50", "total": "900", "description": "<mainText><stats><attention>20</attention>法术强度<br><attention>100%</attention>基础法力回复<br><attention>10</attention>技能急速</stats><br><br></mainText>", "item_desc": "20法术强度\n100%基础法力回复\n10技能急速", "into": ["2065", "4005", "6617", "6620", "322065", "324005", "326617", "326620"], "from": ["1004", "1052", "2022"], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "630"}, {"itemId": "4643", "name": "警觉眼石", "price": "1200", "total": "2300", "description": "<mainText><stats><attention>250</attention>生命值<br><attention>20</attention>技能急速<br><attention>25</attention>护甲<br><attention>30</attention>魔法抗性</stats><br><br><passive>奥术窖藏</passive><br>这件装备至多可储存3个已购买的控制守卫。<br><br><passive>注视：</passive><br>使你的侦察守卫和控制守卫的放置上限提升1。</mainText>", "item_desc": "需要完成来自云游途径的辅助任务\n250生币值\n20技能急速\n25护甲\n30魔法抗性\n\n奥术窖藏\n这件装备至多可储存3个已购买的掉制守卫。\n\n注视:\n使你的侦察守卫和控制守卫的成置上限提升1。", "into": [], "from": ["4638"], "types": ["AbilityHaste", "Active", "Armor", "CooldownReduction", "Health", "SpellBlock", "Vision"], "sell": "1610"}, {"itemId": "4645", "name": "影焰", "price": "900", "total": "3200", "description": "<mainText><stats><attention>110</attention>法术强度<br><attention>15</attention>法术穿透</stats><br><br><passive>余烬绽放</passive><br><magicDamage>魔法伤害</magicDamage>和<trueDamage>真实伤害</trueDamage>会<attention>暴击</attention>低于40%生命值的敌人，造成20%提升伤害。 </mainText>", "item_desc": "110法术强度\n15法术穿透 \n\n余烬绽放\n魔法伤害和真实伤害会暴击低于40%生命值的敌人，造成20%提升伤害。\n暴击伤害修正因素仅影响余烬绽放的额外伤害。", "into": [], "from": ["1058", "3145"], "types": ["MagicPenetration", "SpellDamage"], "sell": "2240"}, {"itemId": "4646", "name": "风暴狂涌", "price": "800", "total": "2800", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention>15</attention>法术穿透<br><attention>6%</attention>移动速度</stats><br><br><passive>风暴掠袭</passive> <br>在2.5秒内对一个英雄造成相当于其25%最大生命值的伤害时，对其施加<passive>风啸</passive>。<br><br><passive>风啸</passive><br>在2秒后，造成<magicDamage>魔法伤害</magicDamage>。如果目标在<passive>风啸</passive>触发前阵亡，那么它会对附近的敌人们造成伤害。</mainText>", "item_desc": "90法术强度\n15法术穿透\n6%移动速度 \n\n风暴掠袭(30秒)\n在2.5秒内对一个英雄造成相当于其25%最大生命值的伤害时，对其施加风啸。\n\n风啸\n在2秒后，造成(125+10%【法术强度】)魔法伤害。如果目标在风啸触发前阵亡，那么它会对附近的敌人们造成伤害。", "into": [], "from": ["3113", "3145"], "types": ["<PERSON><PERSON><PERSON>", "MagicPenetration", "NonbootsMovement", "SpellDamage"], "sell": "1960"}, {"itemId": "6333", "name": "死亡之舞", "price": "275", "total": "3300", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>15</attention>技能急速<br><attention>50</attention>护甲</stats><br><br><passive>无视痛苦</passive><br>所受的一部分伤害会以流血形式在3秒里持续扣除。<br><br><passive>蔑视</passive><br>如果一名在过去3秒内曾被你造成过伤害的英雄阵亡，会净化<passive>无视痛苦</passive>的剩余伤害，并在2秒里持续为你回复<healing>生命值</healing>。</mainText>", "item_desc": "60攻击力\n15技能急速\n50护甲\n\n无视痛苦\n【近战】所受的(30%)伤害会以流血形式在3秒里持续扣除。\n【远程】所受的(10%)伤害会以流血形式在3秒里持续扣除。\n\n蔑视\n如果一名在过去3秒内曾被你造成过伤害的英雄阵亡，会净化无视痛苦的剩余伤害，并在2秒甲持续为你回复共(75% 额外【攻击力】)生命值。", "into": [], "from": ["1037", "2019", "3133"], "types": ["AbilityHaste", "Armor", "Damage"], "sell": "2310"}, {"itemId": "6609", "name": "炼金朋克链锯剑", "price": "350", "total": "3100", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>450</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>劈削</passive><br>对敌方英雄造成物理伤害时会施加<keyword>40%重伤</keyword>效果，持续3秒。</mainText>", "item_desc": "45攻击力\n450生命值\n15技能急速 \n\n劈削\n对敌方英雄造成物理伤害时会施加40%重伤效果，持续3秒。\n重伤:降低治疗效果和生命回复的效能。", "into": [], "from": ["1011", "3123", "3133"], "types": ["AbilityHaste", "CooldownReduction", "Damage", "Health"], "sell": "2170"}, {"itemId": "6610", "name": "焚天", "price": "500", "total": "3100", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>400</attention>生命值<br><attention>10</attention>技能急速</stats><br><br><passive>光盾打击</passive><br>你对一个英雄打出的第一次攻击会<attention>暴击</attention>并<healing>回复生命值</healing>。</mainText>", "item_desc": "40攻击力\n400生命值\n10技能急速 \n\n光盾打击 每个目标(8秒)\n【近战】你对一个英雄打出的第一次攻击会暴击，并回复自身(100% 基础【攻击力】 )+6%已损失生命值。\n【远程】你对一个英雄打出的第一次攻击会暴击，并回复自身(50% 基础【攻击力】 )+6%已损失生命值。\n溢出的治疗效果会被提供为一个临时的额外生命值。", "into": [], "from": ["1028", "2021", "3133"], "types": ["AbilityHaste", "CooldownReduction", "Damage", "Health"], "sell": "2170"}, {"itemId": "6616", "name": "流水法杖", "price": "800", "total": "2250", "description": "<mainText><stats><attention>35</attention>法术强度<br><attention> 10%</attention>治疗和护盾强度<br><attention>125%</attention>基础法力回复<br><attention>15</attention>技能急速</stats><br><br><passive>湍流</passive><br>当你为一名友军提供治疗或护盾效果时，你和你的目标都会获得持续6秒的<magicDamage>45法术强度</magicDamage>。</mainText>", "item_desc": "35法术强度\n10%治疗和护盾强度\n125%基础法力回复\n15技能急速 \n\n湍流\n当你为一名友军提供治疗或护盾效果时，你和你的目标都会获得持续6秒的45法术强度。", "into": [], "from": ["3108", "3114"], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "1575"}, {"itemId": "6617", "name": "月石再生器", "price": "500", "total": "2200", "description": "<mainText><stats><attention>25</attention>法术强度<br><attention>200</attention>生命值<br><attention>20</attention>技能急速<br><attention>125%</attention>基础法力回复</stats><br><br><passive>星光恩典</passive><br>对一名友军的治疗或护盾会连锁至另一名友方英雄(你自己除外)，提供相当于<healing> 30%</healing>原治疗值的治疗效果或<shield> 35%</shield>原护盾值的护盾效果。</mainText>", "item_desc": "25法术强度\n200生命值\n20技能急速\n125%基础法力回复 \n\n星光恩典\n对一名友军的治疗或护盾会连锁至另一名友方英雄(你自己除外)，提供相当于30%原数额的治疗效果或35%原护盾的护盾效果。\n\n如果附近没有其他友军，对相同目标提供相当于30%原治疗值的治疗效果或35%原护盾值的护盾效果。", "into": [], "from": ["3067", "4642"], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen", "SpellDamage"], "sell": "1540"}, {"itemId": "6620", "name": "海力亚的回响", "price": "500", "total": "2200", "description": "<mainText><stats><attention>35</attention>法术强度<br><attention>200</attention>生命值<br><attention>20</attention>技能急速<br><attention>125%</attention>基础法力回复</stats><br><br><passive>灵魂虹吸</passive><br>对一名英雄造成伤害时会提供一块<passive>灵魂碎片</passive>，至多至2块。对一名友军提供治疗或护盾时，会消耗所有<passive>灵魂碎片</passive>来回复<healing>生命值</healing>并对相距最近的那个敌方英雄造成基于碎片数量的<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "35法术强度\n200生命值\n20技能急速\n125%基础法力回复 \n\n灵魂虹吸\n对一名英雄造成伤害时会提供一块灵魂碎片，至多至2块。对一名友军提供治疗或护盾时，会消耗所有灵魂碎片并且回复65x碎片数量的生命值并对相距最近的那个敌方英雄造成50x碎片数量的魔法伤害。\n\n如果你仔细听，你还能在尖叫声中听到城市的喧器。", "into": [], "from": ["3067", "4642"], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen", "SpellDamage"], "sell": "1540"}, {"itemId": "6621", "name": "黎明核心", "price": "450", "total": "2500", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention> 16%</attention>治疗和护盾强度<br><attention>100%</attention>基础法力回复</stats><br><br><passive>最初之光</passive><br>每100%基础法力回复获得<healing>2%治疗和护盾强度</healing>和<scaleAP>10法术强度</scaleAP>。</mainText>", "item_desc": "45法术强度\n16%治疗和护盾强度\n100%基础法力回复 \n\n最初之光\n每100%基础法力回复获得2%治疗和护盾强度和10法术强度。\n\n“世界上所有的能量都来自群星--至少说是这么说的。”", "into": [], "from": ["1026", "3114", "3114"], "types": ["ManaRegen", "SpellDamage"], "sell": "1750"}, {"itemId": "6631", "name": "挺进破坏者", "price": "750", "total": "3300", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>25%</attention>攻击速度<br><attention>450</attention>生命值</stats><br><br><passive>顺劈</passive><br>攻击会对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br> <active>破阵冲击波</active><br>对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>和35%<status>减速</status>。<br>每命中一个英雄就会获得<speed>持续衰减的35%移动速度</speed>，持续3秒。</mainText>", "item_desc": "40攻击力\n25%攻击速度\n450生命值 \n\n顺劈\n【近战】攻击会对附近的敌人们造成了(40%【攻击力】)物理伤害。\n【远程】攻击会对附近的敌人们造成了(20%【攻击力】)物理伤害。\n\n破阵冲击波(15秒)\n对附近的敌人们造成(80%【攻击力】)物理伤害和35%减速。\n每命中一个英雄就会获得持续衰减的35%移动速度，持续3秒。\n\n顺劈不会在建筑物上触发。\n你在施放破阵冲击波期间可以移动。", "into": [], "from": ["1042", "3044", "3077"], "types": ["AttackSpeed", "Damage", "Health", "Slow"], "sell": "2310"}, {"itemId": "6653", "name": "兰德里的折磨", "price": "800", "total": "3000", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>300</attention>生命值</stats><br><br><passive>折磨</passive><br>伤害型技能会灼烧敌人，每秒造成<magicDamage>2%最大生命值的魔法伤害</magicDamage>，持续3秒。<br><br><passive>受苦</passive><br>在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至6%。</mainText>", "item_desc": "60法术强度\n300生命值 \n\n折磨\n伤害型技能会灼烧敌人，每秒造成2%最大生命值的魔法伤害，持续3秒。\n\n受苦\n在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至6%。", "into": [], "from": ["2508", "3147"], "types": ["Health", "SpellDamage"], "sell": "2100"}, {"itemId": "6655", "name": "卢登的配枪", "price": "450", "total": "2750", "description": "<mainText><stats><attention>100</attention>法术强度<br><attention> 600</attention>法力<br><attention>10</attention>技能急速</stats><br><br><passive>开火</passive><br>伤害型技能会发射6层弹丸，对目标和附近的敌人们造成<magicDamage>额外魔法伤害</magicDamage>。剩余【弹丸】会对主要目标发射，造成20%伤害。<br></mainText>", "item_desc": "100法术强度\n600法力\n10技能急速 \n\n开火(12秒)\n伤害型技能会发射6层弹丸，对目标和附近的其它敌人造成额外的(75 +5%【法术强度】)魔法伤害。剩余【弹丸】会对主要目标发射，造成20%伤害。(最大值：150+10%【法术强度】)\n“法师的最佳好友！几天就带它回家！”——皮尔沃特夫商人，推测已故。", "into": [], "from": ["3145", "3802"], "types": ["AbilityHaste", "CooldownReduction", "<PERSON><PERSON>", "SpellDamage"], "sell": "1925"}, {"itemId": "6657", "name": "时光之杖", "price": "450", "total": "2600", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention>350</attention>生命值<br><attention> 400</attention>法力</stats><br><br><passive>时无限</passive><br>这件装备每60秒获得<scaleHealth>10生命值</scaleHealth>、<scaleMana>20法力值</scaleMana>和<scaleAP>3法术强度</scaleAP>，至多至10层。在达到最大层数时，还会使英雄等级提升1级。<br><br><passive>永恒</passive><br>承受来自英雄的伤害时，回复相当于<scaleMana>10%</scaleMana>伤害值的<scaleMana>法力值</scaleMana>。<br>施放一个技能时，<healing>为自身治疗相当于25%法力消耗的生命值</healing>。</mainText>", "item_desc": "45法术强度\n350生命值\n400法力 \n\n时无限\n这件装备每60秒获得10生命值、20法力值和3法术强度，至多至10层。在达到最大层数时，还会使英雄等级提升1级。\n\n永恒\n承受来自英雄的伤害时，你会回复相当于10%伤害值的法力值。\n施放一个技能时，为自身治疗相当于25%法力消耗的生命值。\n\n来自永恒的法力值是按减免前伤害值来算的。\n来自永恒的治疗效果上限为每次施放或每秒(维持型技能)20生命值。", "into": [], "from": ["1026", "3803"], "types": ["Health", "HealthRegen", "<PERSON><PERSON>", "ManaRegen", "SpellDamage"], "sell": "1820"}, {"itemId": "6660", "name": "斑比的熔渣", "price": "250", "total": "900", "description": "<mainText><stats><attention>150</attention>生命值<br><attention>5</attention>技能急速</stats><br><br><passive>献祭</passive><br>承受或造成伤害后，对附近敌人造成<magicDamage>魔法伤害</magicDamage>，持续3秒。</mainText>", "item_desc": "150生命值\n5技能急速 \n\n献祭\n承受或造成伤害后，每秒对附近敌人造成15魔法伤害，持续3秒。\n\n对小兵和野怪造成额外的50%伤害。", "into": ["3068", "6664"], "from": ["1028", "2022"], "types": ["AbilityHaste", "Health"], "sell": "630"}, {"itemId": "6662", "name": "冰脉护手", "price": "800", "total": "2900", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>50</attention>护甲<br><attention>15</attention>技能急速</stats><br><br><passive>咒刃</passive><br>在施放一个技能后，你的下一次攻击造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>并生成一个持续2秒的冰冷地带，这个地带可造成<status>减速</status>。</mainText>", "item_desc": "300生命值\n50护甲\n15技能急速 \n\n咒刃(1.5秒)\n在施放一个技能后，你的下一次攻击造成额外的(150% 基础【攻击力】)\n【近战】物理伤害并生成一个持续2秒的冰冷地带，这个地带可造成(25% )减速。\n【远程】物理伤害并生成一个持续2秒的冰冷地带，这个地带可造成(12.5%)减速。", "into": [], "from": ["1028", "1031", "3057"], "types": ["AbilityHaste", "Armor", "CooldownReduction", "Health", "OnHit", "Slow"], "sell": "2030"}, {"itemId": "6664", "name": "璀璨回响", "price": "650", "total": "2800", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>40</attention>魔法抗性<br><attention>10</attention>技能急速<br><attention>100%</attention>基础生命回复</stats><br><br><passive>献祭</passive><br>承受或造成伤害后，每秒对附近敌人造成<magicDamage>魔法伤害</magicDamage>，持续3秒。<br><br><passive>荒弃</passive><br>击杀一个敌人时，会在其周围造成<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "400生命值\n40魔法抗性\n10技能急速\n100%基础生命回复 \n\n献祭\n承受或造成伤害后，每秒对附近敌人造成(15 +1% 额外【生命值】 )魔法伤害，持续3秒。\n\n荒弃\n击杀一个敌人时，会在其周围造成(30 +2% 额外【生命值】)魔法伤害。\n\n献祭对小兵和野怪造成的伤害提升25%\n\n艾卡西亚的一件圣物，重制版。", "into": [], "from": ["3211", "6660"], "types": ["AbilityHaste", "<PERSON>ra", "Health", "HealthRegen", "MagicResist", "SpellBlock"], "sell": "1960"}, {"itemId": "6665", "name": "千变者贾修", "price": "650", "total": "3200", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>45</attention>护甲<br><attention>45</attention>魔法抗性</stats><br><br><passive>虚空生物的复原力</passive><br>在与英雄战斗5秒后，使你的额外<scaleArmor>护甲</scaleArmor>和<scaleMR>魔法抗性</scaleMR>提升30%，持续到战斗结束为止。<br><br></mainText>", "item_desc": "350生命值\n45护甲\n45魔法抗性 \n\n虚空生物的复原力\n在与英雄的战斗5秒后，使你的额外护甲和魔法抗性提升30%，持续到战斗结束为止。\n\n\"他也许会被叫做很多种东西。一件甲壳头盔。一层护膜。一座活体坟墓......\"", "into": [], "from": ["1011", "1031", "1057"], "types": ["Armor", "Health", "MagicResist", "SpellBlock"], "sell": "2240"}, {"itemId": "6670", "name": "正午箭袋", "price": "350", "total": "1300", "description": "<mainText><stats><attention>15</attention>攻击力<br><attention>20%</attention>暴击几率</stats><br><br></mainText>", "item_desc": "15攻击力\n20%暴击几率", "into": ["3036", "3095", "6673"], "from": ["1018", "1036"], "types": ["CriticalStrike", "Damage"], "sell": "910"}, {"itemId": "6672", "name": "海妖杀手", "price": "425", "total": "3100", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>40%</attention>攻击速度<br><attention>4%</attention>移动速度</stats><br><br><passive>放倒它</passive><br>每第三次攻击造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>，这个伤害会基于目标的已损失生命值获得提升。</mainText>", "item_desc": "45攻击力\n40%攻击速度\n4%移动速度 \n\n放倒它\n【近战】每第三次普攻造成(150-200【等级提升】)物理伤害，基于目标的已损失生命值至多提升至(225-300)【等级提升】。\n【远程】每第三次攻击造成(120-160【等级提升】)额外物理伤害，基于目标的已损失生命值至多提升至(180-240)【等级提升】。", "into": [], "from": ["1043", "3051", "6690"], "types": ["AttackSpeed", "Damage", "NonbootsMovement", "OnHit"], "sell": "2170"}, {"itemId": "6673", "name": "不朽盾弓", "price": "825", "total": "3000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>25%</attention>暴击几率</stats><br><br><passive>救主灵刃</passive><br>在受到将使你的生命值跌到30%以下的伤害时，提供持续3秒的<shield>护盾值</shield>。</mainText>", "item_desc": "55攻击力\n25%暴击几率 \n\n救主灵刃(90秒)\n【近战】受到将使你跌到30%生命值以下的伤害时，获得持续3秒的(400-700【等级提升】)护盾值\n【远程】受到将使你跌到30%生命值以下的伤害时，获得持续3秒的(320-560【等级提升】)护盾值。", "into": [], "from": ["1037", "6670"], "types": ["CriticalStrike", "Damage"], "sell": "2100"}, {"itemId": "6675", "name": "纳沃利烁刃", "price": "950", "total": "2650", "description": "<mainText><stats><attention>40%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br><passive>超凡入圣</passive><br>攻击会使各基础技能的冷却时间缩短<attention>15%</attention>剩余冷却时间。</mainText>", "item_desc": "40%攻击速度\n25%暴击几率\n4%移动速度 \n\n超凡入圣\n攻击会使各基础技能的冷却时间缩短15%剩余冷却时间。", "into": [], "from": ["1042", "1042", "3086"], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "1855"}, {"itemId": "6676", "name": "收集者", "price": "525", "total": "3000", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>10</attention>穿甲<br><attention>25%</attention>暴击几率</stats><br><br><passive>死</passive><br>你的伤害会处决低于5%生命值的英雄。<br><br><passive>税</passive><br>击杀英雄时提供<gold>25额外金币</gold>。</mainText>", "item_desc": "50攻击力\n10穿甲\n25%暴击几率 \n死\n你的伤害会处决低于5%生命值的英雄。\n税\n击杀英雄时提供25额外金币", "into": [], "from": ["1018", "1037", "3134"], "types": ["ArmorPenetration", "CriticalStrike", "Damage"], "sell": "2100"}, {"itemId": "6690", "name": "剑翎", "price": "425", "total": "775", "description": "<mainText><stats><attention>15</attention>攻击力<br><attention>4%</attention>移动速度</stats><br><br></mainText>", "item_desc": "15攻击力\n4%移动速度", "into": ["3087", "3142", "4003", "6672", "6700"], "from": ["1036"], "types": ["Damage", "NonbootsMovement"], "sell": "543"}, {"itemId": "6692", "name": "星蚀", "price": "625", "total": "2900", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>15</attention>技能急速</stats><br><br><passive>永升之月</passive><br>在2秒内用2次独立的攻击或技能命中一名英雄时，会为你提供持续2秒的<shield>护盾值</shield>。</mainText>", "item_desc": "60攻击力\n15技能急速 \n永升之月(6秒)\n在2秒内用2次独立的攻击或技能命中一名英雄时，会造成(6|4)%最大生命值的物理伤害并为你提供持续2秒的((160+40%额外【攻击力】)(80 +20% 额外【攻击力】 ))护盾值。", "into": [], "from": ["1036", "1037", "3133"], "types": ["AbilityHaste", "CooldownReduction", "Damage"], "sell": "2030"}, {"itemId": "6694", "name": "赛瑞尔达的怨恨", "price": "500", "total": "3000", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>35%</attention>护甲穿透<br><attention>15</attention>技能急速</stats><br><br><passive>严寒</passive><br>伤害型技能会对低于50%生命值的敌人造成持续1秒的30%<status>减速</status>。</mainText>", "item_desc": "45攻击力\n35%护甲穿透\n15技能急速 \n严寒\n伤害型技能会对低于50%生命值的敌人造成持续1秒的30%减速。", "into": [], "from": ["3035", "3133"], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "2100"}, {"itemId": "6695", "name": "巨蛇之牙", "price": "625", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>15</attention>穿甲</stats><br><br><passive>掠盾者</passive><br>对一名敌方英雄造成伤害时，会使其获得的护盾值降低0%，持续3秒。<br>如果该敌方英雄尚未被<passive>掠盾者</passive>影响，会使其身上的所有护盾降低0%。</mainText>", "item_desc": "55攻击力\n15穿甲 \n掠盾者\n【近战】对一名敌方英雄造成伤害时，会使其获得的护盾值降低50%，持续3秒。如果该敌方英雄尚未被掠盾者影响，会使其身上的所有护盾降低50%。\n【远程】对一名敌方英雄造成伤害时，会使其获得的护盾值降低35%，持续3秒。如果该敌方英雄尚未被掠盾者影响，会使其身上的所有护盾降低35%。\n魔法护盾不会被削减。", "into": [], "from": ["1037", "3134"], "types": ["ArmorPenetration", "Damage"], "sell": "1750"}, {"itemId": "6696", "name": "公理圆弧", "price": "613", "total": "3000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>18</attention>穿甲<br><attention>20</attention>技能急速</stats><br><br><passive>涌动</passive><br>在你对一名敌方英雄造成伤害后，如果该英雄在3秒内阵亡，则会返还你的终极技能的一些总冷却时间。</mainText>", "item_desc": "55攻击力\n18穿甲\n20技能急速 \n涌动\n在你对一名敌方英雄造成伤害后，如果该英雄在3秒内阵亡，则会返还你(15+15%【穿甲】)%的终极技能总冷却时间。", "into": [], "from": ["2020", "3133"], "types": ["AbilityHaste", "ArmorPenetration", "Damage"], "sell": "2100"}, {"itemId": "6697", "name": "狂妄", "price": "950", "total": "3000", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>18</attention>穿甲<br><attention>10</attention>技能急速</stats><br><br><passive>盛名</passive><br>如果一名在过去3秒内曾被你造成过伤害的英雄阵亡，则获得持续90秒的<physicalDamage>15+2x已击杀英雄数的攻击力</physicalDamage>。</mainText>", "item_desc": "60攻击力\n18穿甲\n10技能急速 \n盛名\n如果一名在过去3秒内曾被你造成过伤害的英雄阵亡，则获得持续90秒的15+2x已击杀英雄数的攻击力。\n已征服的英雄:未知", "into": [], "from": ["3133", "3134"], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "2100"}, {"itemId": "6698", "name": "亵渎九头蛇", "price": "663", "total": "3200", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>18</attention>穿甲<br><attention>10</attention>技能急速</stats><br><br><passive>顺劈</passive><br>攻击会对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br> <active>邪斩</active><br>对你附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。</mainText>", "item_desc": "60攻击力\n18穿甲\n10技能急速 \n顺劈:\n【近战】攻击会对附近的敌人们造成(40%【攻击力】)物理伤害\n【远程】攻击会对附近的敌人们造成(20%【攻击力】)物理伤害\n主动(10秒)\n邪斩\n对你附近的敌人们造成80=(80%【攻击力】)物理伤害。\n顺劈不会在建筑物上触发。\n深渊需要祭品。", "into": [], "from": ["2020", "3077"], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "2240"}, {"itemId": "6699", "name": "电震涡流剑", "price": "963", "total": "3000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>18</attention>穿甲<br><attention>10</attention>技能急速</stats><br><br><passive>激电</passive><br>冲刺和潜行会使<keyword>盈能</keyword>的叠层加快75%。<br><br><passive>苍穹</passive><br>你的<keyword>盈能攻击</keyword>会造成<physicalDamage>额外物理伤害</physicalDamage>，以及持续0.75秒的<keyword>减速</keyword>。</mainText>", "item_desc": "55攻击力\n18穿甲\n10技能急速 \n激电\n冲刺和潜行会使盈能的叠层加快75%。\n苍穹\n你的盈能攻击会造成(100)额外物理伤害，以及持续0.75秒的(【近战】(99)|【远程】(20))%减速。\n盈能:移动和攻击将生成一次盈能攻击。", "into": [], "from": ["1036", "1036", "2020"], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "2100"}, {"itemId": "6701", "name": "禁忌时机", "price": "475", "total": "2700", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>18</attention>穿甲</stats><br><br><passive>伺机待发</passive><br>在脱离与英雄的战斗状态8秒后获得<scaleLethality>穿甲</scaleLethality>。这个穿甲会在对英雄造成伤害后持续3秒。<br><br><passive>险境脱身</passive><br>如果一个英雄受到你伤害后3秒内阵亡，则你获得持续1.5秒的<speed>持续衰减的200移动速度</speed>。<br></mainText>", "item_desc": "55攻击力\n18穿甲\n伺机待发\n【近战】在脱离与英雄的战斗状态8秒后获得11穿甲。这个穿甲会在对英雄造成伤害后持续3秒。\n【远程】在脱离与英雄的战斗状态8秒后获得5穿甲。这个穿甲会在对英雄造成伤害后持续3秒。\n险境脱身\n如果一个英雄受到你伤害后3秒内阵亡，则你获得持续1.5秒的持续衰减的200移动速度。\n有时，你需要调整你自己的……", "into": [], "from": ["1036", "1037", "3134"], "types": ["Active", "ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "1890"}, {"itemId": "8010", "name": "放血者的诅咒", "price": "750", "total": "2900", "description": "<mainText><stats><attention>65</attention>法术强度<br><attention>400</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>恶劣衰朽</passive><br>用技能或被动对英雄造成<magicDamage>魔法伤害</magicDamage>时，会对其造成持续6秒的<scaleMR>7.5%魔法抗性削减</scaleMR>，至多可叠至30 %。</mainText>", "item_desc": "65法术强度\n400生命值\n15技能急速\n恶劣衰朽\n用技能或被动对英雄造成魔法伤害时，会对其造成持续6秒的7.5%魔法抗性削减，至多可叠至30%。\n同一次技能施放仅会每0.3秒对每个英雄施加层数。", "into": [], "from": ["3108", "3147"], "types": ["CooldownReduction", "Health", "MagicPenetration", "SpellDamage"], "sell": "2030"}, {"itemId": "8020", "name": "深渊面具", "price": "1000", "total": "2650", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>45</attention>魔法抗性<br><attention>15</attention>技能急速</stats><br><br><passive>损毁</passive><br>使附近的敌方英雄承受12%额外<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "350生命值\n45魔法抗性\n15技能急速 \n损毁\n使附近的敌方英雄们承受12%额外魔法伤害。\n一名英雄在同一时间只会被一个损毁效果影响。", "into": [], "from": ["1057", "3067"], "types": ["AbilityHaste", "CooldownReduction", "Health", "MagicResist", "SpellBlock"], "sell": "1855"}, {"itemId": "9168", "name": "锁定的武器栏位", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>证明你的实力，厄运小姐就会为你的装备栏升级。<br><br>[已通过完成成就解锁。]</mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9171", "name": "旋风切割器", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>可造成伤害并击退敌人的环绕飞弹。<br><br><status>进化：</status>生命回复<br><br><rules>伤害、技能急速、效果范围、飞弹数量、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9172", "name": "悠米无人机", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>召唤一个智能悠米无人机。智能悠米伤害并击飞敌人，还能帮你收集经验值。<br><br><status>进化：</status>拾取半径<br><br><rules>伤害、技能急速、效果范围、持续时间、拾取半径</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9173", "name": "耀光力场", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>对附近敌人造成伤害，伤害随最大生命值增长。<br><br><status>进化：</status>最大生命值<br><br><rules>伤害、效果范围、最大生命值</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9174", "name": "斯塔缇克之剑", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射闪电，在生命值最高的敌人之间弹跳。<br><br><status>进化：</status>最大生命值<br><br><rules>伤害、技能急速、效果范围、暴击几率、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9175", "name": "雌狮之怨", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>向英雄左右两侧发射新月形飞弹。<br><br><status>进化：</status>技能急速<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9176", "name": "机关兔兔枪", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>在锥形范围内持续造成区域伤害。<br><br><status>进化：</status>持续时间<br><br><rules>伤害、技能急速、持续时间、效果范围</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9177", "name": "炽烈短弓", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射可留下燃烧区域的飞弹。<br><br><status>进化：</status>效果范围<br><br><rules>伤害、技能急速、效果范围、飞弹数量、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9178", "name": "歼灭者", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>可击败所有普通敌人、重创精英的巨大爆炸。冷却时间较长。<br><br><status>进化：</status>经验值<br><br><rules>伤害、技能急速、效果范围</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9179", "name": "战兔十字弩", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝随机方向以锥形发射飞弹，飞弹获得额外暴击几率。飞弹暴击时可穿透敌人。<br><br><status>进化：</status>暴击几率<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9180", "name": "UwU魔爆炮", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝距离最近的敌人快速发射飞弹，对命中的首个目标造成伤害。<br><br><status>进化：</status>技能急速<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9181", "name": "漩涡手套", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射一连串旋转飞弹。<br><br><status>进化：</status>生命回复<br><br><rules>伤害、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9183", "name": "回旋刃", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝距离最近的敌人发射回旋飞弹。<br><br><status>进化：</status>移动速度<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9184", "name": "战兔巨爆", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝随机敌人发射轨道打击，打击获得额外暴击几率。在小范围内造成高额伤害。<br><br><status>进化：</status>暴击几率<br><br><rules>伤害、技能急速、效果范围、暴击几率</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9185", "name": "反鲨海弹", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射在敌人之间弹跳的爆炸物。<br><br><status>进化：</status>伤害<br><br><rules>伤害、技能急速、效果范围</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9187", "name": "提伯斯标准版", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>召唤机器提伯斯。机器提伯斯进行挥击，造成区域伤害。机器提伯斯会专注攻击生命值最高的敌人。<br><br><status>进化：</status>持续时间<br><br><rules>伤害、技能急速、效果范围、持续时间、移动速度</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9188", "name": "幻灵地雷", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>在环形区域内投掷定时爆炸的飞弹，造成大范围伤害。<br><br><status>进化：</status>效果范围<br><br><rules>伤害、技能急速、效果范围、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9189", "name": "最终都市列车", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射一连串旋转飞弹。<br><br><status>进化：</status>生命回复<br><br><rules>伤害、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9190", "name": "回响蝠刃", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射可撞墙反弹的穿刺飞弹。<br><br><status>进化：</status>飞弹数量<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9192", "name": "爪爪投毒器", "price": "0", "total": "0", "description": "", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9193", "name": "冰爆护甲", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>阻挡伤害并冻结敌人。伤害受益于护甲和最大生命值。<br><br><status>进化：</status>护甲<br><br><rules>伤害、技能急速、效果范围、持续时间、护甲</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9271", "name": "不息气旋", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>获得永久环绕飞弹，可对敌人造成伤害和击退。<br><br><rules>伤害、技能急速、飞弹数量、效果范围、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9272", "name": "悠米无人机_最终版_最终最终版", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>召唤一个智能悠米无人机。智能悠米伤害并击飞敌人，同时收集经验值。造成足够的伤害后，掉落一份治疗包。<br><br><rules>伤害、技能急速、效果范围、持续时间、拾取半径</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9273", "name": "爆破之拥", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>对附近敌人造成伤害，伤害值受益于你的最大生命值。在燃烧区域内被击杀的敌人会触发爆炸。<br><br><rules>伤害、效果范围、最大生命值</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9274", "name": "普朗比斯电雕机", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>在目标敌人周围制造风暴，持续造成伤害。<br><br><rules>伤害、技能急速、效果范围、暴击几率、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9275", "name": "包覆之光", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射一道跨越屏幕的光束。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9276", "name": "双重兔兔弹幕", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>在锥形范围内持续造成范围伤害。受到伤害的敌人会被减速并最终晕眩。晕眩的敌人将受到额外伤害。<br><br><rules>伤害、技能急速、持续时间、效果范围</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9277", "name": "进化余烬射击", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射可留下燃烧区域的飞弹。燃烧区域的规模和伤害随时间增加。<br><br><rules>伤害、技能急速、效果范围、飞弹、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9278", "name": "幻灵启示录", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>可击败所有普通敌人、重创精英的巨大爆炸。被击杀的敌人会掉落更多经验值，并有几率掉落金币。冷却时间较长。<br><br><rules>伤害、技能急速、效果范围</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9279", "name": "战兔至尊弩炮", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝随机方向以锥形发射飞弹，飞弹获得额外暴击几率。飞弹暴击时可穿透敌人。<br><br><status>进化：</status>暴击几率<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9280", "name": "OwO魔爆炮", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>以极快的速度朝最近的敌人发射飞弹，对命中的的首个目标造成伤害。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9281", "name": "风暴护手", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>在自己周围发射两股源源不断的导弹。流向以相反方向旋转。<br><br><rules>伤害、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9283", "name": "四重回旋刃", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝距离最近的敌人发射回旋飞弹，飞弹爆炸后放出更小的飞弹。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9284", "name": "疾速兔降", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>朝随机敌人发射轨道轰炸，最终发动一次大规模打击。<br><br><rules>伤害、技能急速、效果范围、暴击几率</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9285", "name": "不休踩弹", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射爆炸物。只要能找到目标，这个爆炸物就会在敌人之间无休止地弹跳。<br><br><rules>伤害、技能急速、效果范围</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9287", "name": "提伯斯（顶配版）", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>召唤愤怒的机器提伯斯。机器提伯斯进行挥击，造成区域伤害。愤怒的机器提伯斯会集中攻击生命值最高的敌人，并在持续时间内体型变大、速度变快、并造成更多伤害。<br><br><rules>伤害、技能急速、效果范围、持续时间、移动速度</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9288", "name": "金克丝三连炸", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>在环形区域内投掷定时爆炸的飞弹，爆炸后额外释放爆炸，造成巨大的区域伤害。<br><br><rules>伤害、技能急速、效果范围、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9289", "name": "最终都市特快", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>一列火车随机穿过敌人。火车在行进中产生爆炸，并击飞敌人。被击杀的敌人有一定几率掉落金币。<br><br><rules>伤害、技能急速、暴击几率、护甲</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9290", "name": "薇恩的炫彩刃", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>发射在墙体间弹跳的飞弹，对穿过的所有敌人造成伤害。箭矢每次弹跳都会造成更多伤害。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9292", "name": "熊掌化学喷雾器", "price": "0", "total": "0", "description": "", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9293", "name": "深度冻结", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>阻挡伤害并冻结敌人，同时提供一道护盾。伤害受益于护甲，护盾受益于最大生命值。护盾失效时，再次冻结附近的所有敌人。<br><br><rules>伤害、技能急速、效果范围、持续时间、最大生命值、护甲</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9300", "name": "喵喵枪", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>金克丝朝目标方向连续扫射。<br><br><status>进化：</status>技能急速<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9301", "name": "盾牌猛击", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>蕾欧娜猛击盾牌，在锥形区域内造成伤害，伤害受益于护甲。<br><br><status>进化：</status>护甲<br><br><rules>伤害、技能急速、效果范围、暴击几率、护甲</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9302", "name": "声波", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>萨勒芬妮发射声波，对击中的敌人造成伤害和减速。<br><br><status>进化：</status>飞弹数量<br><br><rules>伤害、技能急速、暴击几率、飞弹数量、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9303", "name": "木枷挥击", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>贝蕾亚横扫前方，造成的伤害随最大生命值增长。<br><br><status>进化：</status>最大生命值<br><br><rules>伤害、技能急速、效果范围、暴击几率、最大生命值</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9304", "name": "斩钢闪", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>亚索每秒获得25剑意，移动和冲刺时获得更多剑意。<br><br>剑意达到100时，放出一道旋风，对一条直线上的所有敌人造成伤害。<br><br><status>进化：</status>暴击几率<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9305", "name": "触手重击", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>俄洛伊用神像猛击地面，在冲击范围内造成伤害，并生成一条触手攻击附近敌人。<br><br><status>进化：</status>持续时间<br><br><rules>伤害、技能急速、效果范围、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9306", "name": "带翼羽刃", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>霞朝前方扔出一把穿透敌人的羽刃，对后续敌人造成的伤害降低，并在地面留下一把羽刃。<br><br><status>进化：</status>拾取半径<br><br><rules>伤害、技能急速、暴击几率、飞弹数量、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9307", "name": "附灵飞弹", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>阿萝拉朝空中发射灵体，引导其飞行。被命中的敌人将受到伤害，并被<keywordMajor>附灵</keywordMajor>。每第三次施放，她会净化<keywordMajor>被附灵</keywordMajor>的目标，造成额外伤害。<br><br><status>进化：</status>经验获取<br><br><rules>伤害、技能急速、暴击几率、飞弹、经验值</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9308", "name": "兔子跳", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>锐雯被动获得移动速度，并在移动时获得充能。充能达到最大时，锐雯向前跃出，在一定范围内造成伤害。每第二次施放造成击飞。<br><br><status>进化：</status>移动速度<br><br><rules>伤害、效果范围、暴击几率、移动速度</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9400", "name": "战猫弹幕", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>金克丝朝目标方向连续扫射。子弹穿透敌人，命中首个目标后，对后续目标造成的伤害降低。冷却时间大幅缩短。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9401", "name": "雄狮之光", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>击中较大区域并对敌人施加日光。友军或其它武器可引爆日光，造成额外伤害。<br><br><rules>伤害、技能急速、效果范围、暴击几率、护甲</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9402", "name": "幻灵回响", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>声波现在会在到达终点后返回，再次造成伤害并穿过萨勒芬妮。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9403", "name": "无情砍削", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>贝蕾亚横扫前方，造成可叠加的流血效果并造成伤害，伤害随最大生命值增长。<br><br><rules>伤害、技能急速、效果范围、暴击几率、最大生命值</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9404", "name": "漂泊风暴", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>亚索精炼自己的能量，射出数量更少旦规模更大的龙卷风。龙卷风在空中旋弧前进，并在终点留下造成伤害的暴风。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量、效果范围、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9405", "name": "巨熊重击", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>俄洛伊用神像猛击地面，在冲击范围内造成伤害，并生成一条触手来攻击附近敌人。有几率召唤出巨型触手，它造成更高的伤害。<br><br><rules>伤害、技能急速、效果范围、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9406", "name": "爱意飞射", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>霞现在还会在身后投出一把佣兽羽刃。这把特殊的羽刃会在敌人之间弹跳并造成伤害，随后返回霞身边，提供一个较小护盾。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量、持续时间</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9407", "name": "跳跃灵体", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>阿萝拉现在能射出更多灵体，执行净化时对<keywordMajor>被附灵</keywordMajor>目标的周边区域造成伤害。<br><br><rules>伤害、技能急速、暴击几率、飞弹数量、持续时间、经验值</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "9408", "name": "胡萝卜撞击", "price": "0", "total": "0", "description": "<mainText><stats></stats><br><br>锐雯被动获得移动速度，并在移动时获得充能。充能达到最大时，锐雯向前跃出，在一定范围内造成伤害。每第二次施放都会击飞敌人，使区域破裂并产生二次爆炸，再次造成伤害和击飞。<br><br><rules>伤害、效果范围、暴击几率、移动速度</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": [], "sell": "0"}, {"itemId": "126697", "name": "狂妄", "price": "900", "total": "2950", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>18</attention>穿甲<br><attention>15</attention>技能急速</stats><br><br><passive>盛名</passive><br>击杀英雄时获得基于英雄击杀数的临时攻击力。</mainText>", "item_desc": "", "into": [], "from": ["3133", "3134"], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "2065"}, {"itemId": "220000", "name": "属性加成", "price": "750", "total": "750", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，用来获得一个永久属性加成。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "ArmorPenetration", "AttackSpeed", "Consumable", "CooldownReduction", "CriticalStrike", "Damage", "Health", "MagicPenetration", "MagicResist", "<PERSON><PERSON>", "NonbootsMovement", "SpellBlock", "SpellDamage", "SpellVamp", "Tenacity"], "sell": "375"}, {"itemId": "220001", "name": "传说级战士装备", "price": "2000", "total": "2000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件传说级战士装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "ArmorPenetration", "AttackSpeed", "Consumable", "CooldownReduction", "Damage", "Health", "LifeSteal", "MagicResist", "<PERSON><PERSON>", "NonbootsMovement", "OnHit", "SpellBlock", "SpellVamp", "Tenacity"], "sell": "2000"}, {"itemId": "220002", "name": "传说级射手装备", "price": "2000", "total": "2000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件传说级射手装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "ArmorPenetration", "AttackSpeed", "Consumable", "CooldownReduction", "CriticalStrike", "Damage", "Health", "LifeSteal", "<PERSON><PERSON>", "NonbootsMovement", "OnHit"], "sell": "2000"}, {"itemId": "220003", "name": "传说级刺客装备", "price": "2000", "total": "2000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件传说级刺客装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "ArmorPenetration", "AttackSpeed", "Consumable", "CooldownReduction", "Damage", "Health", "NonbootsMovement", "SpellBlock"], "sell": "2000"}, {"itemId": "220004", "name": "传说级法师装备", "price": "2000", "total": "2000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件传说级法师装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "ArmorPenetration", "AttackSpeed", "Consumable", "CooldownReduction", "CriticalStrike", "Health", "MagicPenetration", "MagicResist", "<PERSON><PERSON>", "NonbootsMovement", "OnHit", "Slow", "SpellBlock", "SpellDamage", "SpellVamp"], "sell": "2000"}, {"itemId": "220005", "name": "传说级坦克装备", "price": "2000", "total": "2000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件传说级坦克装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "Consumable", "CooldownReduction", "CriticalStrike", "Health", "MagicPenetration", "MagicResist", "NonbootsMovement", "Slow", "SpellBlock", "SpellDamage"], "sell": "2000"}, {"itemId": "220006", "name": "传说级辅助装备", "price": "2000", "total": "2000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件传说级辅助装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "AttackSpeed", "Consumable", "CooldownReduction", "Health", "MagicResist", "ManaRegen", "NonbootsMovement", "OnHit", "Slow", "SpellBlock", "SpellDamage"], "sell": "2000"}, {"itemId": "220007", "name": "棱彩装备", "price": "4000", "total": "4000", "description": "<mainText><stats></stats><br><br><active>主动 - 消耗：</active> 自动开启一次半随机的选择，以选取一件棱彩装备。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "ArmorPenetration", "AttackSpeed", "Consumable", "CooldownReduction", "CriticalStrike", "Damage", "Health", "HealthRegen", "LifeSteal", "MagicPenetration", "MagicResist", "<PERSON><PERSON>", "ManaRegen", "NonbootsMovement", "OnHit", "Slow", "SpellBlock", "SpellDamage", "SpellVamp", "Tenacity"], "sell": "4000"}, {"itemId": "221038", "name": "暴风之剑", "price": "1300", "total": "1300", "description": "<mainText><stats><attention>40</attention>攻击力</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage"], "sell": "910"}, {"itemId": "222051", "name": "守护者号角", "price": "500", "total": "500", "description": "<mainText><stats><attention>300</attention>生命值</stats><br><br><passive>复原力</passive><br>每5秒回复<healing>20生命值</healing>。<br><br><passive>不屈不挠</passive><br>格挡来自英雄的攻击和技能的12伤害(对抗持续伤害技能时的效能为25%)。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "HealthRegen", "Lane"], "sell": "250"}, {"itemId": "222065", "name": "舒瑞娅的战歌", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>法术强度<br><attention>15</attention>技能急速<br><attention>6%</attention>移动速度<br><attention>150%</attention>基础法力回复</stats><br><br> <active>鼓舞演讲</active><br>为附近的友军提供若干秒移动速度。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "CooldownReduction", "ManaRegen", "NonbootsMovement", "SpellDamage"], "sell": "1250"}, {"itemId": "222141", "name": "帽子饮品", "price": "500", "total": "500", "description": "<mainText><stats></stats><br><br><rules>帮助你出人头地。</rules><active>主动 - 消耗：</active>这个饮品什么也不做。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Consumable", "Damage"], "sell": "500"}, {"itemId": "222502", "name": "无终恨意", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>25</attention>护甲<br><attention>10</attention>技能急速<br><attention>25</attention>魔法抗性</stats><br><br><passive>苦楚</passive><br>在与英雄的战斗状态下，每4秒对附近的敌方英雄们造成<magicDamage>魔法伤害</magicDamage>，并治疗自身<healing>相当于250%已造成伤害的生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "CooldownReduction", "Health", "MagicResist"], "sell": "1750"}, {"itemId": "222503", "name": "黯炎火炬", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention> 600</attention>法力<br><attention>20</attention>技能急速</stats><br><br><passive>邪焰</passive><br>技能伤害会使敌人们持续灼烧。这个伤害对野怪提升。<br><br><passive>黯炎</passive><br>被你的<passive>邪焰</passive>影响的每个敌方英雄、史诗级野怪、大型野怪都会为你提供<scaleAP>法术强度</scaleAP>。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "<PERSON><PERSON>", "SpellDamage"], "sell": "1250"}, {"itemId": "222504", "name": "败魔", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>80</attention>魔法抗性</stats><br><br><passive>法师之祸</passive><br>如果连续15秒没有受到魔法伤害，获得<shield>魔法护盾</shield>。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "HealthRegen", "SpellBlock"], "sell": "1250"}, {"itemId": "223002", "name": "引路者", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>45</attention>护甲<br><attention>6%</attention>移动速度</stats><br><br><passive>引路</passive><br>在移动时，积攒至多<speed>20额外移动速度</speed>。<br>在最大速度时：<li>生成一条路径来使你的友方英雄们加快<speed>移动速度，数额相当于你的15%移动速度</speed>。<li>如果你是近战英雄，你的下一次攻击还会对目标造成持续1秒的50%<keyword>减速</keyword>。<br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Health", "NonbootsMovement"], "sell": "1750"}, {"itemId": "223003", "name": "大天使之杖", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>法术强度<br><attention> 600</attention>法力<br><attention>25</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得相当于<scaleMana>2%额外法力值的法术强度。</scaleMana><br><br><passive>法力积攒</passive><br><buffedStat>在2个对战回合后</buffedStat>，这个装备转变为<rarityLegendary>炽天使之拥</rarityLegendary>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "<PERSON><PERSON>", "SpellDamage"], "sell": "1250"}, {"itemId": "223004", "name": "魔宗", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention> 600</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得<scaleAD>相当于0的额外攻击力</scaleAD>。<br><br><passive>法力积攒</passive><br><buffedStat>在2个对战回合后</buffedStat>，这个装备会转变为<rarityLegendary>魔切</rarityLegendary>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Damage", "<PERSON><PERSON>", "OnHit"], "sell": "1250"}, {"itemId": "223005", "name": "鬼蟹", "price": "500", "total": "500", "description": "<mainText><stats><attention>70</attention>移动速度</stats><br><br> <active>墙体行走：</active>  (0秒)<br>获得持续6秒的穿墙行走能力。在墙体内时，获得300移动速度。施放一个技能或进行攻击将结束这个效果。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Boots"], "sell": "350"}, {"itemId": "223006", "name": "狂战士胫甲", "price": "500", "total": "500", "description": "<mainText><stats><attention>35%</attention>攻击速度<br><attention>55</attention>移动速度</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Boots"], "sell": "350"}, {"itemId": "223009", "name": "轻灵之靴", "price": "500", "total": "500", "description": "<mainText><stats><attention>70</attention>移动速度</stats><br><br>所受的移动减速效果的强度降低<buffedStat>40%</buffedStat>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Boots"], "sell": "350"}, {"itemId": "223020", "name": "法师之靴", "price": "500", "total": "500", "description": "<mainText><stats><attention>20</attention>法术穿透<br><attention>55</attention>移动速度</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Boots", "MagicPenetration"], "sell": "350"}, {"itemId": "223026", "name": "守护天使", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>45</attention>护甲</stats><br><br><passive>拯救恩典：</passive><br>你的英雄在受到致命伤害时，会在凝滞4秒后原地复活，恢复<healing>50%基础生命值</healing>和<scaleMana>100%最大法力值</scaleMana>。这效果有一回合的冷却时间。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Damage"], "sell": "1250"}, {"itemId": "223031", "name": "无尽之刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>25%</attention>暴击几率<br><attention>40%</attention>暴击伤害</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "223032", "name": "育恩塔尔荒野箭", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>25%</attention>攻击速度</stats><br><br><passive>熟能生巧</passive><br>攻击时，永久获得暴击几率，至多至25%。<br><br><passive>疾风骤雨</passive> <br>攻击一个敌方英雄时，获得持续5秒的攻击速度。攻击可使这个冷却时间缩短。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "223033", "name": "凡性的提醒", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>30%</attention>护甲穿透<br><attention>25%</attention>暴击几率</stats><br><br><passive>脓毒</passive><br>对敌方英雄造成物理伤害时会施加<status>40%重伤</status>效果，持续3秒。<br><br><buffedStat>如果一个敌人在持续被<status>重伤</status>效果影响下治疗的生命值超过其60%最大生命值，那么此效果提升至<status>80%重伤</status>。</buffedStat><br><br><rules><status>重伤</status>会降低治疗效果和生命回复的效能。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "223036", "name": "多米尼克领主的致意", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>40%</attention>护甲穿透<br><attention>25%</attention>暴击几率</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "223039", "name": "阿塔玛的清算", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>700</attention>生命值<br><attention>20%</attention>暴击几率</stats><br><br><passive>大手</passive><br>获得 0-30%暴击几率，受益于你的<scaleHealth>额外生命值</scaleHealth>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CriticalStrike", "Health", "Lane"], "sell": "1250"}, {"itemId": "223046", "name": "幻影之舞", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60%</attention>攻击速度<br><attention>8%</attention>移动速度<br><attention>25%</attention>暴击几率</stats><br><br><passive>幽影华尔兹</passive>变为<keyword>幽灵</keyword>状态。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "1250"}, {"itemId": "223047", "name": "铁板靴", "price": "500", "total": "500", "description": "<mainText><stats><attention>25</attention>护甲<br><attention>30</attention>移动速度</stats><br><br>使即将到来的攻击伤害降低<buffedStat>14%</buffedStat>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Boots"], "sell": "350"}, {"itemId": "223050", "name": "基克的聚合", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>25</attention>护甲<br><attention>15</attention>技能急速<br><attention>30</attention>魔法抗性</stats><br><br><passive>霜火风暴</passive><br>施放你的终极技能时会召唤一个风暴环绕于你。这个风暴对敌方英雄们造成每秒<magicDamage>魔法伤害</magicDamage>和<keyword>减速</keyword>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "Health", "SpellBlock"], "sell": "1250"}, {"itemId": "223053", "name": "斯特拉克的挑战护手", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>20%</attention>韧性</stats><br><br><passive>抓人双爪</passive><br>获得<scaleAD>0额外攻击力</scaleAD>。<br><br><passive>救主灵刃</passive>  (0秒)<br>在受到将使你的生命值跌到30%以下的伤害时，提供在4.5秒里持续衰减的<shield>0护盾值</shield>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health", "Tenacity"], "sell": "1250"}, {"itemId": "223065", "name": "振奋盔甲", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>40</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br><passive>无拘活力</passive><br>使你所受的全部治疗和护盾的效果提升30%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "HealthRegen", "SpellBlock"], "sell": "1750"}, {"itemId": "223068", "name": "日炎圣盾", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>40</attention>护甲<br><attention>10</attention>技能急速</stats><br><br><passive>献祭</passive><br>承受或造成伤害后，每秒对附近敌人造成<magicDamage>0魔法伤害</magicDamage>，持续3秒。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "<PERSON>ra", "Health"], "sell": "1250"}, {"itemId": "223071", "name": "黑色切割者", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>350</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><passive>切割</passive><br>对英雄造成<physicalDamage>物理伤害</physicalDamage>时会施加<scaleArmor>6%护甲削减</scaleArmor>，持续6秒，至多至<scaleArmor>30%护甲削减</scaleArmor>。<br><br><passive>热烈</passive><br>造成<physicalDamage>物理伤害</physicalDamage>时会提供持续2秒的<speed>20移动速度</speed>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage", "Health", "NonbootsMovement", "OnHit"], "sell": "1250"}, {"itemId": "223072", "name": "饮血剑", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>70</attention>攻击力<br><attention>18%</attention>生命偷取</stats><br><br><passive>灵液护盾</passive><br>将来自你的生命偷取的溢出治疗效果转化为护盾值。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "LifeSteal"], "sell": "1250"}, {"itemId": "223073", "name": "海克斯注力刚壁", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>20%</attention>攻击速度<br><attention>450</attention>生命值</stats><br><br><passive>海克斯充能</passive><br>获得30终极技能急速。<br><br><passive>过载</passive><br>在施放你的终极技能后，获得持续8秒的<attackSpeed>30%攻击速度</attackSpeed>和<speed>15%移动速度</speed>。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "AttackSpeed", "CooldownReduction", "Damage", "Health", "NonbootsMovement"], "sell": "1750"}, {"itemId": "223074", "name": "贪欲九头蛇", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>15</attention>技能急速<br><attention>15%</attention>生命偷取</stats><br><br><passive>顺劈：</passive>攻击和技能会对附近的其它敌人造成物理伤害。主动使用以命中附近的敌人们。<br> <active>血斩</active><br>对你周围的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br>你的生命偷取会作用于此伤害。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Damage", "LifeSteal", "OnHit"], "sell": "1250"}, {"itemId": "223075", "name": "荆棘之甲", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>60</attention>护甲</stats><br><br><passive>荆棘：</passive><br>在被一次攻击命中后，对攻击者造成魔法伤害并且如果目标是英雄，还会施加40%<status>重伤</status>效果。<br><br><rules><status>重伤</status>会降低治疗效果和生命回复的效能。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Health"], "sell": "1250"}, {"itemId": "223078", "name": "三相之力", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>35</attention>攻击力<br><attention>25%</attention>攻击速度<br><attention>333</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><passive>咒刃</passive><br>在施放一个技能后，你的下一次普通攻击会获得强化，造成额外物理伤害。<br><br><passive>加快</passive><br>普攻会提供移动速度。<br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "AttackSpeed", "CooldownReduction", "Damage", "Health", "NonbootsMovement", "OnHit"], "sell": "1250"}, {"itemId": "223084", "name": "心之钢", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>700</attention>生命值</stats><br><br><passive>庞然吞食</passive> 每个目标 (0秒)<br>当附近有一名敌方英雄时，随时间持续充能一次对该英雄的强力攻击。充能攻击会造成额外物理伤害，并为你提供永久最大生命值。<br><br><passive>歌利亚巨人</passive><br>获得基于总生命值的体型提升。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health"], "sell": "1250"}, {"itemId": "223085", "name": "卢安娜的飓风", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br><passive>风怒</passive><br>你的普通攻击会朝目标附近的至多2个敌人发射弩箭。这些弩箭能够附带攻击特效并且可以暴击。<br><br><rules>这件装备仅远程英雄可用。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement", "OnHit"], "sell": "1250"}, {"itemId": "223087", "name": "斯塔缇克电刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>40%</attention>攻击速度<br><attention>4%</attention>移动速度</stats><br><br><passive>电火花</passive><br>攻击<OnHit>命中时</OnHit>会触发连锁闪电，造成<magicDamage>魔法伤害</magicDamage>，有冷却时间。<br><br><passive>电击</passive><br>在对目标造成伤害的3秒内参与击杀，会重置电火花的冷却时间。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "NonbootsMovement", "OnHit"], "sell": "1250"}, {"itemId": "223089", "name": "灭世者的死亡之帽", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>65</attention>法术强度</stats><br><br><passive>魔法乐章</passive><br>使你的总<scaleAP>法术强度提升30%</scaleAP>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["SpellDamage"], "sell": "1250"}, {"itemId": "223091", "name": "智慧末刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50%</attention>攻击速度<br><attention>40</attention>魔法抗性<br><attention>20%</attention>韧性</stats><br><br><br><li><passive>喧争：</passive>攻击附带魔法伤害并提供移动速度。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "OnHit", "SpellBlock", "Tenacity"], "sell": "1250"}, {"itemId": "223094", "name": "疾射火炮", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>30%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br><passive>盈能</passive><br>移动和攻击会生成一次<keyword>盈能攻击</keyword>。<br><br><passive>神射手</passive><br>你的<keyword>盈能攻击</keyword>附带<magicDamage>200额外魔法伤害</magicDamage>。此外，【盈能】攻击至多获得35%额外攻击距离。<br><br><buffedStat>【盈能】的叠层速度在【斗魂竞技场】中翻倍。</buffedStat><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "1250"}, {"itemId": "223095", "name": "岚切", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>25%</attention>攻击速度<br><attention>25%</attention>暴击几率</stats><br><br><passive>盈能</passive><br>移动和攻击会生成一次<keyword>盈能攻击</keyword>。<br><br><passive>电冲</passive><br>你的<keyword>盈能攻击</keyword>附带<magicDamage>0额外魔法伤害</magicDamage>并提供持续1.5秒的<speed>45%移动速度</speed>。<br><br><buffedStat>【盈能】的叠层速度在【斗魂竞技场】中翻倍。</buffedStat><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "223100", "name": "巫妖之祸", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>10%</attention>移动速度<br><attention>20</attention>技能急速</stats><br><br><passive>咒刃</passive>  (0秒)<br>施放技能后，你的下一次普通攻击因强化而附带额外的<magicDamage>0魔法伤害</magicDamage><OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "NonbootsMovement", "OnHit", "SpellDamage"], "sell": "1250"}, {"itemId": "223102", "name": "女妖面纱", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>40</attention>魔法抗性</stats><br><br><passive>废除</passive><br>提供一层法术护盾来格挡下一个敌方技能。<br><br><rules>如果你在此装备冷却完毕之前受到来自英雄的伤害，那么它的冷却时间会重新开始计算。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["SpellBlock", "SpellDamage"], "sell": "1250"}, {"itemId": "223107", "name": "救赎", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>15</attention>技能急速<br><attention>100%</attention>基础法力回复<br><attention> 16%</attention>治疗和护盾强度</stats><br><br><br><br> <active>主动</active>  (0秒)<br><active>干涉</active><br>选择范围内的一个区域作为目标。在2.5秒后，召下一道光束来治疗友方英雄们并伤害敌方英雄们。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen"], "sell": "1250"}, {"itemId": "223109", "name": "骑士之誓", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>50</attention>护甲<br><attention>15</attention>技能急速</stats><br><br><passive>牺牲</passive><br>当你的<attention>誓约者</attention>在附近时，将其所受的12%伤害转移到你身上，并为你提供相当于其对英雄的10%已造成伤害值的<healing>治疗效果</healing>。<br><br> <active>主动</active> (0秒)<br><active>立誓</active><br>指定一名友方英雄作为你的<attention>誓约者</attention>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "<PERSON>ra", "CooldownReduction", "Health"], "sell": "1250"}, {"itemId": "223110", "name": "冰霜之心", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>90</attention>护甲<br><attention> 450</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>凛冬之抚</passive><br>附近敌人的<attackSpeed>攻击速度</attackSpeed>降低。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "<PERSON>ra", "CooldownReduction", "<PERSON><PERSON>"], "sell": "1250"}, {"itemId": "223111", "name": "水银之靴", "price": "500", "total": "500", "description": "<mainText><stats><attention>30</attention>魔法抗性<br><attention>30</attention>移动速度<br><attention>30%</attention>韧性</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Boots", "SpellBlock", "Tenacity"], "sell": "350"}, {"itemId": "223112", "name": "守护者法球", "price": "500", "total": "500", "description": "<mainText><stats><attention>55</attention>法术强度<br><attention>25</attention>技能急速</stats><br><br><passive>复原力</passive><br>每5秒回复<scaleMana>10法力值</scaleMana>。如果你不能获得法力值，则转而回复<healing>15生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CooldownReduction", "Lane", "ManaRegen", "SpellDamage"], "sell": "250"}, {"itemId": "223115", "name": "纳什之牙", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention>45%</attention>攻击速度<br><attention>10</attention>技能急速</stats><br><br><passive>艾卡西亚之咬</passive><br>攻击附带魔法伤害<OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "AttackSpeed", "OnHit", "SpellDamage"], "sell": "1250"}, {"itemId": "223116", "name": "瑞莱的冰晶节杖", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>350</attention>生命值</stats><br><br><passive>凝霜</passive><br>伤害型技能会使敌人<status>减速</status>30%，持续1秒。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "Slow", "SpellDamage"], "sell": "1250"}, {"itemId": "223118", "name": "残疫", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention> 600</attention>法力<br><attention>20</attention>技能急速</stats><br><br><passive>蔑视</passive><br>你的终极技能获得技能急速。<br><br><passive>憎恨之雾</passive><br>在用你的终极技能对一个英雄造成伤害时，<keywordMajor>灼烧</keywordMajor>其脚下的地面，造成伤害并削减其魔法抗性。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "<PERSON><PERSON>", "SpellDamage"], "sell": "1250"}, {"itemId": "223119", "name": "凛冬之临", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>400</attention>生命值<br><attention> 600</attention>法力<br><attention>15</attention>技能急速</stats><br><br><li><passive>敬畏</passive>获得<scaleHealth>相当于总法力值的额外生命值</scaleHealth>。<li><passive>法力积攒</passive><buffedStat>在2个对战回合后</buffedStat>，这个装备转变为<rarityLegendary>末日寒冬</rarityLegendary>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Health", "<PERSON><PERSON>"], "sell": "1250"}, {"itemId": "223124", "name": "鬼索的狂暴之刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>20</attention>攻击力<br><attention>25</attention>法术强度<br><attention>25%</attention>攻击速度</stats><br><br>攻击附带<magicDamage>30魔法伤害<OnHit>攻击特效</OnHit></magicDamage>。<br><br><passive>沸腾打击</passive><br>普通攻击会提供<attackSpeed>8%攻击速度</attackSpeed>，至多可叠加4次并至多提供<attackSpeed>0攻击速度</attackSpeed>。在满层状态下，每第三次攻击会附带2次<OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "OnHit", "SpellDamage"], "sell": "1250"}, {"itemId": "223135", "name": "虚空之杖", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>65</attention>法术强度<br><attention>40%</attention>法术穿透</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["MagicPenetration", "SpellDamage"], "sell": "1250"}, {"itemId": "223137", "name": "蜕生", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>30%</attention>法术穿透<br><attention>15</attention>技能急速</stats><br><br><passive>死中焕生</passive><br>每当一个敌方英雄在被你造成伤害后的3秒内阵亡时，在其所在位置生成一个新星，来为友军们<healing>治疗生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "MagicPenetration", "SpellDamage"], "sell": "1250"}, {"itemId": "223139", "name": "水银弯刀", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>40</attention>魔法抗性<br><attention>10%</attention>生命偷取</stats><br><br><br><br><active>主动 - </active> <active>水银：</active>移除你英雄身上的所有控制类减益效果并提供移动速度。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "Damage", "LifeSteal", "NonbootsMovement", "SpellBlock", "Tenacity"], "sell": "1250"}, {"itemId": "223142", "name": "幽梦之灵", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>22</attention>穿甲<br><attention>4%</attention>移动速度</stats><br><br><passive>鬼影萦绕</passive><br>非战斗状态期间获得<speed>0移动速度</speed>。<br><br><br> <active>主动</active>  (0秒)<br><active>鬼魂步伐</active><br>提供移动速度和幽灵状态。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "1250"}, {"itemId": "223143", "name": "兰顿之兆", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>75</attention>护甲</stats><br><br><passive>复原力</passive><br>暴击对你造成的伤害降低30%。<br><br><active>谦卑</active><br><status>减速</status>附近的敌人们。<br><br> <active>主动</active> (0秒)<br><active>谦卑</active><br>对附近敌人造成持续2秒的70%<keyword>减速</keyword>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "Armor", "Health", "Slow"], "sell": "1250"}, {"itemId": "223146", "name": "海克斯科技枪刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention>45</attention>攻击力<br><attention> 15%</attention>全能吸血</stats><br><br><br><br> <active>主动</active>  (0秒)<br><active>闪电弹</active><br>震击目标敌方英雄，造成0魔法伤害和持续2秒的40%减速。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "Damage", "LifeSteal", "SpellDamage", "SpellVamp"], "sell": "1250"}, {"itemId": "223152", "name": "海克斯科技火箭腰带", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>95</attention>法术强度<br><attention>400</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><active>主动 - </active> <active>超音速：</active>朝着目标方向冲刺，释放一道魔法弹圆弧，造成魔法伤害。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health", "NonbootsMovement", "SpellDamage"], "sell": "1250"}, {"itemId": "223153", "name": "破败王者之刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>20%</attention>攻击速度<br><attention>10%</attention>生命偷取</stats><br><br><passive>雾之锋</passive><br>攻击附带额外的敌人当前生命值的物理伤害 <OnHit>攻击特效</OnHit>。<br><br><passive>抓挠之影</passive>  (0秒)<br>你对一名敌方英雄打出的第一次普攻会对其造成减速。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "LifeSteal", "OnHit", "Slow"], "sell": "1250"}, {"itemId": "223156", "name": "玛莫提乌斯之噬", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>15</attention>技能急速<br><attention>40</attention>魔法抗性</stats><br><br><passive>救主灵刃</passive><br>在受到将使你的生命值跌到30%以下的魔法伤害时，获得魔法伤害护盾。当<passive>救主灵刃</passive>触发时，获得全能吸血，持续到战斗结束为止。  </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Damage", "LifeSteal", "SpellBlock", "SpellVamp"], "sell": "1250"}, {"itemId": "223157", "name": "中娅沙漏", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>50</attention>护甲</stats><br><br><active>主动 - </active><active>凝滞：</active>在2.5秒里<status>免疫伤害</status>且<status>不可被选取</status>，但在此期间里无法采取任何其它行动<buffedStat>(120秒)</buffedStat>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "Armor", "SpellDamage"], "sell": "1250"}, {"itemId": "223158", "name": "明朗之靴", "price": "500", "total": "500", "description": "<mainText><stats><attention>40</attention>技能急速<br><attention>45</attention>移动速度</stats><br><br>获得10召唤师技能急速。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Boots", "CooldownReduction"], "sell": "350"}, {"itemId": "223161", "name": "朔极之矛", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>200</attention>生命值</stats><br><br><passive>龙之力量</passive><br>获得25基础技能急速。<br><br><passive>专注意志</passive><br>使用技能造成伤害会使你的英雄技能和被动伤害提升3%，持续6秒。（叠加4次）。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Damage", "Health"], "sell": "1250"}, {"itemId": "223165", "name": "莫雷洛秘典", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention>250</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>苦难</passive><br>对敌方英雄造成魔法伤害时会施加持续3秒的<status>40%重伤</status>效果。<br><br><buffedStat>如果一个敌人在持续被<status>重伤</status>效果影响下治疗的生命值超过其60%最大生命值，那么此效果提升至<status>80%重伤</status>。</buffedStat><br><br><rules><status>重伤</status>会降低治疗效果和生命回复的效能。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "SpellDamage"], "sell": "1250"}, {"itemId": "223172", "name": "灵风", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50%</attention>攻击速度<br><attention>10%</attention>移动速度<br><attention>30</attention>技能急速<br><attention>20%</attention>韧性</stats><br><br><passive>迅捷如风</passive><br>获得持续6秒的5%移动速度<OnHit>攻击特效</OnHit>，至多可叠至25%移动速度。<br><br><rules>韧性会减少<status>晕眩</status>、<status>减速</status>、<status>嘲讽</status>、<status>恐惧</status>、<status>沉默</status>、<status>致盲</status>、<status>变形</status>和<status>定身</status>效果的持续时间。它对<status>浮空</status>或<status>压制</status>效果无效。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "CooldownReduction", "NonbootsMovement", "OnHit", "Tenacity"], "sell": "1250"}, {"itemId": "223177", "name": "守护者之刃", "price": "500", "total": "500", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>250</attention>生命值<br><attention>15</attention>技能急速</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Damage", "Health", "Lane"], "sell": "250"}, {"itemId": "223181", "name": "破舰者", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>500</attention>生命值<br><attention>4%</attention>移动速度</stats><br><br><passive>船长</passive><br>对英雄和史诗级野怪的每第五次攻击造成<physicalDamage>0额外物理伤害</physicalDamage>，对抗建筑物时提升至<physicalDamage>0</physicalDamage>。<br><br><passive>单人派对</passive><br>在你附近1000码内没有友方英雄时，你获得0<scaleArmor>护甲</scaleArmor>和<scaleMR>魔法抗性</scaleMR>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health", "NonbootsMovement"], "sell": "1250"}, {"itemId": "223184", "name": "守护者战锤", "price": "500", "total": "500", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>35%</attention>攻击速度<br><attention>5%</attention>生命偷取</stats><br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health", "Lane", "LifeSteal"], "sell": "250"}, {"itemId": "223185", "name": "守护者短匕", "price": "500", "total": "500", "description": "<mainText><stats><attention>25</attention>攻击力<br><attention>11</attention>穿甲<br><attention>10</attention>技能急速</stats><br><br><passive>农业镰刀</passive><br>使能量花卉的效果提升20%。在攻击一个植物后获得持续3秒的100移动速度。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage", "Lane"], "sell": "250"}, {"itemId": "223190", "name": "钢铁烈阳之匣", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>25</attention>护甲<br><attention>35</attention>魔法抗性<br><attention>25</attention>技能急速</stats><br><br><active>虔诚</active><br>为附近友军提供持续衰减的<shield>护盾值</shield>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "<PERSON>ra", "Health", "MagicResist", "SpellBlock"], "sell": "1250"}, {"itemId": "223222", "name": "米凯尔的祝福", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>100%</attention>基础法力回复<br><attention> 16%</attention>治疗和护盾强度</stats><br><br> <active>主动</active> (0秒)<br><active>纯化</active><br>移除一名友方英雄身上的所有控制类减益效果(<status>击飞</status>和<status>压制</status>除外)并回复生命值。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health", "ManaRegen", "Tenacity"], "sell": "1250"}, {"itemId": "223302", "name": "界弓", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>25%</attention>攻击速度</stats><br><br><passive>晦影</passive><br>攻击造成<magicDamage>30额外魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。<br><br><passive>交相</passive><br>对抗英雄时，在<keywordMajor>光明</keywordMajor>特效和<keywordMajor>黑暗</keywordMajor>特效之间轮换：<li>附带<keywordMajor>光明</keywordMajor>特效的攻击提供持续5秒的 <scaleArmor>护甲</scaleArmor>和<scaleMR>魔抗</scaleMR>。<li>附带<keywordMajor>黑暗</keywordMajor>特效的攻击提供持续5秒的8%<scaleArmor>护甲穿透</scaleArmor>和<scaleMR>法术穿透</scaleMR>。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "AttackSpeed", "Damage", "MagicPenetration", "OnHit"], "sell": "1250"}, {"itemId": "223504", "name": "炽热香炉", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>法术强度<br><attention> 12%</attention>治疗和护盾强度<br><attention>150%</attention>基础法力回复<br><attention>6%</attention>移动速度</stats><br><br><passive>圣洁化</passive><br>对一名友方英雄施加治疗或护盾时，会使你们两个都获得持续6秒的强化，使你们的普攻获得<attackSpeed>40%</attackSpeed>攻击速度和<magicDamage>25魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "ManaRegen", "NonbootsMovement", "SpellDamage"], "sell": "1250"}, {"itemId": "223508", "name": "夺萃之镰", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>20</attention>技能急速<br><attention>25%</attention>暴击几率</stats><br><br><passive>精萃汲取</passive><br>普通攻击附带返还法力值。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "CriticalStrike", "Damage", "ManaRegen", "OnHit"], "sell": "1250"}, {"itemId": "223742", "name": "亡者的板甲", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>40</attention>护甲<br><attention>7%</attention>移动速度</stats><br><br><passive>沉船者</passive><br>在移动时，积攒移动速度。你的下一次攻击会释放掉已积攒的移动速度来造成伤害。如果持有者是近战且达到满层速度，那么该次攻击还会<status>减速</status>目标。<br><br><passive>永不沉没</passive><br>你所受的移动减速效果的强度会被减弱。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Health", "NonbootsMovement", "Slow"], "sell": "1250"}, {"itemId": "223748", "name": "巨型九头蛇", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>400</attention>生命值</stats><br><br><br><li><passive>巨像：</passive>获得基于<scaleAD>额外生命值的攻击力</scaleAD>。<li><passive>顺劈：</passive>攻击附带额外伤害<OnHit>攻击特效</OnHit>，并生成一道冲击波来对目标身后的敌人们造成物理伤害。 <active>刚斩</active><br>强化你的下一次<passive>顺劈</passive>，使其造成<physicalDamage>额外物理伤害</physicalDamage> <OnHit>攻击特效</OnHit>并对目标身后的敌人们造成<physicalDamage>额外物理伤害</physicalDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health", "HealthRegen", "OnHit"], "sell": "1250"}, {"itemId": "223814", "name": "夜之锋刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>14</attention>穿甲<br><attention>375</attention>生命值</stats><br><br><passive>废除</passive> (0秒)<br>提供一层【法术护盾】来格挡下一个敌方技能。<br><br><rules>如果你在这件装备冷却完毕之前受到伤害，那么它的冷却时间会重新开始计算。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage", "Health"], "sell": "1250"}, {"itemId": "224004", "name": "幽魂弯刀", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>21</attention>穿甲</stats><br><br><active>主动 - </active> <active>灵魂之锚</active>  (0秒)<br>标记你的当前位置。在5秒后，回到被标记的位置。你可以在<active>灵魂之锚</active>期间的任一时间点进行重新施放以提前回到你标记的位置。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage"], "sell": "1250"}, {"itemId": "224005", "name": "帝国指令", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>35</attention>技能急速<br><attention>150%</attention>基础法力回复</stats><br><br><passive>协同开火</passive> 每个目标 (0秒)<br><keyword>减速</keyword>或<keyword>定身</keyword>一名敌方英雄时会将其标记5秒。友方英雄的伤害会引爆标记，造成<magicDamage>相当于10%当前生命值的魔法伤害</magicDamage>。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "1250"}, {"itemId": "224401", "name": "自然之力", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>50</attention>魔法抗性<br><attention>4%</attention>移动速度</stats><br><br><passive>吸收</passive><br>受到来自敌方英雄的<magicDamage>魔法伤害</magicDamage>时会为你提供一层持续7秒的<attention>坚定</attention>(至多至10层)。敌人的<status>定身</status>效果会提供额外的2层。<br><br><passive>消散</passive><br>在10层<attention>坚定</attention>的状态下，获得<scaleMR>50魔法抗性</scaleMR>和14%移动速度提升。<br><br><rules>一个技能每1秒可添加一层新的<attention>坚定</attention>。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "NonbootsMovement", "SpellBlock"], "sell": "1250"}, {"itemId": "224403", "name": "金铲铲", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>90</attention>攻击力<br><attention>125</attention>法术强度<br><attention>60%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>250</attention>生命值<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention> 250</attention>法力<br><attention>20</attention>技能急速<br><attention>10%</attention>移动速度<br><attention>10%</attention>生命偷取<br><attention>100%</attention>基础生命回复<br><attention>100%</attention>基础法力回复</stats><br><br><passive>正在搞事</passive><br>你正处在永久的火力全开中！<br><br><flavorText>“它必须做点儿什么……”<br>“错了，它什么都做得到！”</flavorText></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "AttackSpeed", "CooldownReduction", "CriticalStrike", "Damage", "Health", "HealthRegen", "LifeSteal", "<PERSON><PERSON>", "ManaRegen", "NonbootsMovement", "SpellBlock", "SpellDamage"], "sell": "1750"}, {"itemId": "224628", "name": "视界专注", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>25</attention>技能急速</stats><br><br><br><li><passive>高能射击：</passive>用一个无目标类技能对相距600码之外的一名英雄造成伤害、或将一名英雄<status>减速或定身</status>时，会使该英雄<keywordStealth>显形</keywordStealth>并使你对其造成的伤害提升。<br><br><rules>触发<passive>高能射击</passive>的该次技能也会享受到这个伤害提升效果。宠物和非定身类陷阱不会触发这个效果。领域类技能只有在初始放置时会触发这个效果。距离是从技能施放位置处进行测量的。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "SpellDamage"], "sell": "1250"}, {"itemId": "224629", "name": "星界驱驰", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>65</attention>法术强度<br><attention>350</attention>生命值<br><attention>35</attention>技能急速<br><attention>4%</attention>移动速度</stats><br><br><passive>咒舞</passive><br>对英雄造成<magicDamage>魔法伤害</magicDamage>或<trueDamage>真实伤害</trueDamage>时会提供<speed>0移动速度</speed>，持续4秒。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Health", "NonbootsMovement", "SpellDamage"], "sell": "1250"}, {"itemId": "224633", "name": "裂隙制造者", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>350</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>虚空腐蚀</passive><br>在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至8%。在最大强度时，获得<omnivamp>全能吸血</omnivamp>。<br><br><passive>虚空灌注</passive><br>获得相当于你2%<scaleHealth>额外生命值</scaleHealth>的<scaleAP>法术强度</scaleAP>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CooldownReduction", "Health", "SpellDamage", "SpellVamp"], "sell": "1250"}, {"itemId": "224645", "name": "影焰", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention>10</attention>法术穿透</stats><br><br><passive>余烬绽放</passive><br><magicDamage>魔法伤害</magicDamage>和<trueDamage>真实伤害</trueDamage>会<attention>暴击</attention>低于40%生命值的敌人，造成15%提升伤害。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["MagicPenetration", "SpellDamage"], "sell": "1250"}, {"itemId": "224646", "name": "风暴狂涌", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>15</attention>法术穿透<br><attention>4%</attention>移动速度</stats><br><br><passive>风暴掠袭</passive> <br>在2.5秒内对一个英雄造成相当于其25%最大生命值的伤害时，对其施加<passive>风啸</passive>并提供持续1.5秒的<speed>25%移动速度</speed>。<br><br><passive>风啸</passive><br>在2秒后，造成<magicDamage>魔法伤害</magicDamage>。如果目标在<passive>风啸</passive>触发前阵亡，那么它会对附近的敌人们造成伤害并提供<gold>125金币</gold>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["<PERSON><PERSON><PERSON>", "MagicPenetration", "NonbootsMovement", "SpellDamage"], "sell": "1250"}, {"itemId": "226333", "name": "死亡之舞", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>10</attention>技能急速<br><attention>45</attention>护甲</stats><br><br><passive>无视痛苦</passive><br>所受的伤害会以流血形式持续扣除。<br><br><passive>蔑视：</passive><br>参与击杀英雄后会净化<passive>无视痛苦</passive>的剩余伤害，并持续回复生命值。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "Damage"], "sell": "1250"}, {"itemId": "226609", "name": "炼金朋克链锯剑", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>450</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>劈削</passive><br>对敌方英雄造成物理伤害时会施加40%<status>重伤</status>效果，持续3秒。<br><br><buffedStat>如果一个敌人在持续被<status>重伤</status>效果影响下治疗的生命值超过其60%最大生命值，那么此效果提升至<status>80%重伤</status>。</buffedStat><br><br><rules><status>重伤</status>会降低治疗效果和生命回复的效能。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Damage", "Health"], "sell": "1250"}, {"itemId": "226610", "name": "焚天", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>350</attention>生命值<br><attention>10</attention>技能急速</stats><br><br><passive>光盾打击</passive><br>你对一个英雄打出的第一次攻击会<attention>暴击</attention>并<healing>回复生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Damage", "Health"], "sell": "1250"}, {"itemId": "226616", "name": "流水法杖", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>法术强度<br><attention> 14%</attention>治疗和护盾强度<br><attention>150%</attention>基础法力回复<br><attention>10%</attention>移动速度</stats><br><br><passive>湍流</passive><br>当你为一名友军提供治疗或护盾效果时，你和你的目标都会获得持续4秒的<magicDamage>0法术强度</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "1250"}, {"itemId": "226617", "name": "月石再生器", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>35</attention>法术强度<br><attention>200</attention>生命值<br><attention>30</attention>技能急速<br><attention>125%</attention>基础法力回复</stats><br><br><passive>星光恩典</passive><br>对你自己或一名友军的<healing>治疗或护盾</healing>会连锁至另一个英雄，提供相当于<healing>20%</healing>原治疗值的治疗效果或<shield>25%</shield>原护盾值的护盾效果。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen", "SpellDamage"], "sell": "1250"}, {"itemId": "226620", "name": "海力亚的回响", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>法术强度<br><attention>300</attention>生命值<br><attention>30</attention>技能急速<br><attention>150%</attention>基础法力回复</stats><br><br><br><li><passive>灵魂虹吸：</passive>对一名英雄造成伤害时会提供一块<passive>灵魂碎片</passive>。对一名友军提供治疗或护盾时，会消耗所有<passive>灵魂碎片</passive>来回复生命值并对相距最近的那个敌方英雄造成基于碎片数量的魔法伤害。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen", "SpellDamage"], "sell": "1250"}, {"itemId": "226621", "name": "黎明核心", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention> 16%</attention>治疗和护盾强度<br><attention>200%</attention>基础法力回复</stats><br><br><passive>最初之光</passive><br>每100%基础法力回复获得<healing>3%治疗和护盾强度</healing>和<scaleAP>10法术强度</scaleAP>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ManaRegen", "SpellDamage"], "sell": "1250"}, {"itemId": "226630", "name": "渴血战斧", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>400</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><br><br><active>主动 - </active> <active>饥渴斩击：</active>对附近的敌人们造成伤害。每命中一个敌方英雄就会回复生命值。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "CooldownReduction", "Damage", "Health"], "sell": "400"}, {"itemId": "226631", "name": "挺进破坏者", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>15%</attention>攻击速度<br><attention>375</attention>生命值</stats><br><br><passive>顺劈</passive><br>攻击会对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br> <active>破阵冲击波</active><br>对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>和40%<status>减速</status>。<br>每命中一个英雄就会获得<speed>持续衰减的40%移动速度</speed>，持续3秒。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "Health", "NonbootsMovement", "Slow"], "sell": "1250"}, {"itemId": "226653", "name": "兰德里的苦楚", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>法术强度<br><attention>250</attention>生命值</stats><br><br><passive>折磨</passive><br>伤害型技能会<keywordMajor>灼烧</keywordMajor>敌人，每秒造成<magicDamage>2%最大生命值的魔法伤害</magicDamage>，持续3秒。<br><br><passive>受苦</passive><br>在与敌方英雄作战时，每过1秒，就会造成2%额外伤害，至多至6%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "SpellDamage"], "sell": "1250"}, {"itemId": "226655", "name": "卢登的配枪", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>85</attention>法术强度<br><attention> 600</attention>法力<br><attention>25</attention>技能急速</stats><br><br><passive>开火</passive><br>伤害型技能会消耗所有充能来对目标和附近的其它敌人(其它敌人的数量相当于【弹丸】层数)造成额外魔法伤害。如果距离内的目标数量不足，那么每剩余一层【弹丸】，就会对主要目标重复造成一次该伤害。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "<PERSON><PERSON>", "SpellDamage"], "sell": "1250"}, {"itemId": "226657", "name": "时光之杖", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>60</attention>法术强度<br><attention>350</attention>生命值<br><attention> 300</attention>法力</stats><br><br><passive>时无限</passive><br><buffedStat>在2个对战回合后</buffedStat>，这个装备获得额外的<magicDamage>50法术强度</magicDamage>、<scaleHealth>300生命值</scaleHealth>、<scaleMana>400法力值</scaleMana>，并且你的等级提升1级。<br><br><passive>永恒</passive><br>承受来自英雄的伤害时，你会回复相当于7%折前伤害值的<scaleMana>法力值</scaleMana>。施放一个技能时，你会治疗自身相当于25%<scaleMana>法力</scaleMana>消耗的生命值，每次施放每秒最多治疗<scaleHealth>20生命值</scaleHealth>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "HealthRegen", "<PERSON><PERSON>", "ManaRegen", "SpellDamage"], "sell": "1250"}, {"itemId": "226662", "name": "冰脉护手", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>45</attention>护甲<br><attention>10</attention>技能急速</stats><br><br><passive>咒刃</passive><br>在施放一个技能后，你的下一次攻击造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>并生成一个持续2秒的冰冷地带，这个地带可造成<status>减速</status>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "CooldownReduction", "Health", "OnHit", "Slow"], "sell": "1250"}, {"itemId": "226664", "name": "璀璨回响", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>450</attention>生命值<br><attention>40</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br><passive>献祭</passive><br>承受或造成伤害后，每秒对附近敌人造成<magicDamage>魔法伤害</magicDamage>，持续3秒。<br><br><passive>荒弃</passive><br>击杀一个敌人时，会在其周围造成<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "<PERSON>ra", "Health", "MagicResist", "SpellBlock"], "sell": "1250"}, {"itemId": "226665", "name": "千变者贾修", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>35</attention>护甲<br><attention>35</attention>魔法抗性</stats><br><br><passive>虚空生物的复原力</passive><br>在与英雄战斗5秒后，使你的额外<scaleArmor>护甲</scaleArmor>和<scaleMR>魔法抗性</scaleMR>提升40%，持续到战斗结束为止。<br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Health", "MagicResist", "SpellBlock"], "sell": "1250"}, {"itemId": "226672", "name": "海妖杀手", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>30</attention>攻击力<br><attention>35%</attention>攻击速度<br><attention>7%</attention>移动速度</stats><br><br><passive>放倒它</passive><br>每第三次攻击造成<physicalDamage>额外物理伤害</physicalDamage><OnHit>攻击特效</OnHit>，这个伤害会基于目标的已损失生命值获得提升。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "NonbootsMovement", "OnHit"], "sell": "1250"}, {"itemId": "226673", "name": "不朽盾弓", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>25%</attention>暴击几率</stats><br><br><passive>救主灵刃</passive><br>在受到将使你的生命值跌到30%以下的伤害时，提供持续3秒的<shield>护盾值</shield>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "226675", "name": "纳沃利烁刃", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>35%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>4%</attention>移动速度</stats><br><br><passive>超凡入圣</passive><br>你的攻击会使你的各个非终极技能的冷却时间缩短<attention>15%</attention>剩余冷却时间。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "CriticalStrike", "NonbootsMovement"], "sell": "1250"}, {"itemId": "226676", "name": "收集者", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>12</attention>穿甲<br><attention>25%</attention>暴击几率</stats><br><br><passive>死</passive><br>你的伤害会处决低于5%生命值的英雄。<br><br><passive>税</passive><br>击杀英雄时提供<gold>125额外金币</gold>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "CriticalStrike", "Damage"], "sell": "1250"}, {"itemId": "226692", "name": "星蚀", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>10</attention>技能急速</stats><br><br><passive>永升之月</passive><br>在2秒内用2次独立的攻击或技能命中一名英雄时，会为你提供持续2秒的<shield>护盾值</shield>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Damage"], "sell": "1250"}, {"itemId": "226693", "name": "暗行者之爪", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>22</attention>穿甲<br><attention>15</attention>技能急速</stats><br><br><active>沙之挥击</active><br>冲刺穿过目标敌人，造成目标一部分<scaleHealth>最大生命值</scaleHealth>的<physicalDamage>额外物理伤害</physicalDamage>。接下来的3秒里，你对目标造成的伤害提升。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "400"}, {"itemId": "226694", "name": "赛瑞尔达的怨恨", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>30%</attention>护甲穿透<br><attention>10</attention>技能急速</stats><br><br><passive>严寒</passive><br>伤害型技能会对低于50%生命值的敌人造成持续1秒的30%<status>减速</status>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "1250"}, {"itemId": "226695", "name": "巨蛇之牙", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>19</attention>穿甲</stats><br><br><passive>掠盾者</passive><br>对一名敌方英雄造成伤害时，会使其获得的任何护盾降低。当你对一名未被掠盾者影响的敌人造成伤害时，会使其身上的所有护盾降低。<br><br>装备对近战携带者和远程携带者会有不同的性能。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage"], "sell": "1250"}, {"itemId": "226696", "name": "公理圆弧", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>18</attention>穿甲<br><attention>20</attention>技能急速</stats><br><br><passive>格言</passive><br><buffedStat>你的终极技能造成20%提升伤害</buffedStat>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "Damage"], "sell": "1250"}, {"itemId": "226697", "name": "狂妄", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>18</attention>穿甲<br><attention>10</attention>技能急速</stats><br><br><passive>盛名</passive><br>如果一名在过去3秒内曾被你造成过伤害的英雄阵亡，则获得<physicalDamage>15攻击力</physicalDamage>，<buffedStat>持续到回合结束</buffedStat>，或被击杀为止。每当这个效果触发时，【盛名】的<physicalDamage>攻击力永久提升2</physicalDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "1250"}, {"itemId": "226698", "name": "亵渎九头蛇", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>18</attention>穿甲<br><attention>15</attention>技能急速</stats><br><br><passive>顺劈</passive><br>攻击会对附近的敌人们造成<physicalDamage>物理伤害</physicalDamage>。<br> <active>邪斩</active>  (0秒)<br>对你附近的敌人们造成<physicalDamage>0物理伤害</physicalDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "1250"}, {"itemId": "226699", "name": "电震涡流剑", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>18</attention>穿甲<br><attention>20</attention>技能急速</stats><br><br><passive>激电</passive><br>冲刺和潜行会使<keyword>盈能</keyword>的叠层加快75%。<br><br><passive>苍穹</passive><br>你的<keyword>盈能攻击</keyword>会造成<physicalDamage>额外物理伤害</physicalDamage>，以及持续0.75秒的<keyword>减速</keyword>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "1250"}, {"itemId": "226701", "name": "禁忌时机", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>15</attention>穿甲</stats><br><br><passive>伺机待发</passive><br>在脱离与英雄的战斗状态8秒后获得<scaleLethality>穿甲</scaleLethality>。这个穿甲会在对英雄造成伤害后持续3秒。<br><br><passive>险境脱身</passive><br>如果一个英雄受到你伤害后3秒内阵亡，则你获得持续1.5秒的<speed>持续衰减的200移动速度</speed>。<br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "1250"}, {"itemId": "228001", "name": "厌恨锁链", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>650</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><passive>复仇令：</passive><br>降低你所受的来自【仇敌】的伤害。你的【仇敌】在你附近时会降低韧性。<br><br><rules>主动效果的施放距离无限。</rules><br><br><flavorText>“她曾发誓要用她的一生来毁灭他……”</flavorText><br><active>主动 - </active> <active>起誓：</active>选择一个仇敌(90秒)。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health"], "sell": "1250"}, {"itemId": "228002", "name": "沃格勒特的巫师帽", "price": "6000", "total": "6000", "description": "<mainText><stats><ornnBonus>300</ornnBonus>法术强度<br><ornnBonus>50</ornnBonus>护甲<br><ornnBonus>20</ornnBonus>技能急速</stats><br><br><passive>魔法乐章：</passive><br>使你的总<scaleAP>法术强度提升50%</scaleAP>)。<br><br><b>需要强化符文：<prismatic>任务：沃格勒特的巫师帽</prismatic></b><br> <br><active>时间停止</active><br>进入<keyword>凝滞</keyword>状态2.5秒。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "Armor", "SpellDamage"], "sell": "4200"}, {"itemId": "228020", "name": "深渊面具", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>50</attention>魔法抗性<br><attention>15</attention>技能急速</stats><br><br><passive>损毁</passive><br>附近的敌方英雄受到的<magicDamage>魔法伤害</magicDamage>提升12%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "MagicResist", "SpellBlock", "SpellDamage"], "sell": "1250"}, {"itemId": "322065", "name": "舒瑞娅的战歌", "price": "800", "total": "2600", "description": "<mainText><stats><attention>65</attention>法术强度<br><attention>15</attention>技能急速<br><attention>6%</attention>移动速度<br><attention>150%</attention>基础法力回复</stats><br><br> <active>鼓舞演讲</active><br>为附近友军们提供持续4秒的<speed>30%移动速度</speed>。</mainText>", "item_desc": "", "into": [], "from": ["3113", "4642"], "types": ["AbilityHaste", "Active", "CooldownReduction", "ManaRegen", "NonbootsMovement", "SpellDamage"], "sell": "1820"}, {"itemId": "323002", "name": "引路者", "price": "1000", "total": "2600", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>45</attention>护甲<br><attention>6%</attention>移动速度</stats><br><br><passive>引路</passive><br>在移动时，积攒至多<speed>20额外移动速度</speed>。<br>在最大速度时：<li>生成一条路径来使你的友方英雄们加快<speed>移动速度，数额相当于你的15%移动速度</speed>。<li>如果你是近战英雄，你的下一次攻击还会对目标造成持续1秒的50%<keyword>减速</keyword>。<br><br></mainText>", "item_desc": "", "into": [], "from": ["1031", "3066"], "types": ["Armor", "Health", "NonbootsMovement"], "sell": "1820"}, {"itemId": "323003", "name": "大天使之杖", "price": "450", "total": "2900", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention> 600</attention>法力<br><attention>25</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得相当于<scaleMana>1%额外法力值</scaleMana>的法术强度。<br><br><passive>法力流</passive> (8秒，最大5层充能)<br>技能在命中时提供<scaleMana>9.5最大法力值</scaleMana>(对英雄时翻倍)。<br>在<scaleMana>360最大法力值</scaleMana>时转变为<rarityLegendary>炽天使之拥</rarityLegendary>。</mainText>", "item_desc": "", "into": [], "from": ["3108", "3802", "323070"], "types": ["AbilityHaste", "<PERSON><PERSON>", "SpellDamage"], "sell": "2030"}, {"itemId": "323004", "name": "魔宗", "price": "1100", "total": "2900", "description": "<mainText><stats><attention>35</attention>攻击力<br><attention> 500</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得<scaleAD>0额外攻击力</scaleAD>。<br><br><passive>法力流</passive>  (8秒，最大4层)<br>普攻和技能在命中时会提供<scaleMana>6.5最大法力值</scaleMana>(对英雄时翻倍)。<br>在<scaleMana>360最大法力值</scaleMana>时转变为<rarityLegendary>魔切</rarityLegendary>。</mainText>", "item_desc": "", "into": [], "from": ["1036", "3133", "323070"], "types": ["AbilityHaste", "CooldownReduction", "Damage", "<PERSON><PERSON>", "OnHit"], "sell": "2030"}, {"itemId": "323050", "name": "基克的聚合", "price": "800", "total": "2300", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br><passive>霜火风暴</passive><br>施放你的终极技能时会召唤一个风暴环绕于你5秒。这个风暴对敌方英雄们造成每秒<magicDamage>30魔法伤害</magicDamage>和30%<keyword>减速</keyword>。</mainText>", "item_desc": "", "into": [], "from": ["1028", "3105"], "types": ["AbilityHaste", "Armor", "Health", "SpellBlock"], "sell": "1610"}, {"itemId": "323070", "name": "女神之泪", "price": "400", "total": "400", "description": "<mainText><stats><attention> 240</attention>法力</stats><br><br><passive>法力流</passive>  (8秒，最大4层)<br>技能在命中时会提供<scaleMana>6.5最大法力值</scaleMana>(对英雄时翻倍)，至多至<scaleMana>360</scaleMana>。<br><br><passive>帮助之手</passive><br>攻击会对小兵造成额外的<physicalDamage>5物理伤害</physicalDamage>。</mainText>", "item_desc": "", "into": ["3003", "3004", "323003", "323004", "323119"], "from": [], "types": ["<PERSON><PERSON>", "ManaRegen"], "sell": "280"}, {"itemId": "323075", "name": "荆棘之甲", "price": "650", "total": "2650", "description": "<mainText><stats><attention>200</attention>生命值<br><attention>85</attention>护甲</stats><br><br><passive>荆棘</passive><br>在被一次攻击命中后，对攻击者造成<magicDamage>魔法伤害</magicDamage>，并且如果目标是英雄，还会施加持续3秒的40%<keyword>重伤</keyword>效果。</mainText>", "item_desc": "", "into": [], "from": ["1028", "1031", "3076"], "types": ["Armor", "Health"], "sell": "1855"}, {"itemId": "323107", "name": "救赎", "price": "1000", "total": "2800", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>15</attention>技能急速<br><attention>100%</attention>基础法力回复<br><attention> 10%</attention>治疗和护盾强度</stats><br><br> <active>干涉</active> <br>在2.5秒后，为友方单位们回复<healing>200 - 400生命值</healing>，并对敌方英雄们造成<trueDamage>10%最大生命值的真实伤害</trueDamage>。</mainText>", "item_desc": "", "into": [], "from": ["1028", "3067", "3114"], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen"], "sell": "1960"}, {"itemId": "323109", "name": "骑士之誓", "price": "1000", "total": "2900", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>50</attention>护甲<br><attention>10</attention>技能急速<br><attention>150%</attention>基础生命回复</stats><br><br><passive>牺牲</passive><br>当你的<attention>誓约者</attention>在附近时，将其所受的12%伤害转移到你身上，并为你提供相当于其对英雄的10%已造成伤害值的<healing>治疗效果</healing>。 <active>立誓</active> (0秒)<br>指定一名友方英雄作为你的<attention>誓约者</attention>。</mainText>", "item_desc": "", "into": [], "from": ["1006", "1031", "3067"], "types": ["AbilityHaste", "Active", "Armor", "<PERSON>ra", "CooldownReduction", "Health", "HealthRegen"], "sell": "2030"}, {"itemId": "323110", "name": "冰霜之心", "price": "800", "total": "2700", "description": "<mainText><stats><attention>75</attention>护甲<br><attention> 500</attention>法力<br><attention>25</attention>技能急速</stats><br><br><passive>凛冬之抚</passive><br>使附近敌方英雄的<attackSpeed>攻击速度</attackSpeed>降低<attackSpeed>20%</attackSpeed>。</mainText>", "item_desc": "", "into": [], "from": ["3024", "3082"], "types": ["AbilityHaste", "Armor", "<PERSON>ra", "CooldownReduction", "<PERSON><PERSON>"], "sell": "1890"}, {"itemId": "323119", "name": "凛冬之临", "price": "300", "total": "2400", "description": "<mainText><stats><attention>550</attention>生命值<br><attention> 500</attention>法力<br><attention>15</attention>技能急速</stats><br><br><passive>敬畏</passive><br>获得<scaleHealth>0生命值</scaleHealth>。<br><br><passive>法力流</passive>  (8秒，最大4层)<br>普攻和技能在命中时会提供<scaleMana>6.5最大法力值</scaleMana>(对英雄时翻倍)。<br>在<scaleMana>360最大法力值</scaleMana>时转变为<rarityLegendary>末日寒冬</rarityLegendary>。</mainText>", "item_desc": "", "into": [], "from": ["1011", "3067", "323070"], "types": ["AbilityHaste", "Health", "<PERSON><PERSON>"], "sell": "1680"}, {"itemId": "323190", "name": "钢铁烈阳之匣", "price": "1100", "total": "2600", "description": "<mainText><stats><attention>250</attention>生命值<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention>10</attention>技能急速</stats><br><br> <active>虔诚</active><br>为附近友军提供<shield>200 - 360护盾值</shield>，在2.5秒里持续衰减。</mainText>", "item_desc": "", "into": [], "from": ["1029", "1033", "3067"], "types": ["AbilityHaste", "Active", "Armor", "<PERSON>ra", "Health", "MagicResist", "SpellBlock"], "sell": "1820"}, {"itemId": "323222", "name": "米凯尔的祝福", "price": "1000", "total": "2800", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>100%</attention>基础法力回复<br><attention> 15%</attention>治疗和护盾强度<br><attention>15</attention>技能急速</stats><br><br> <active>纯化</active> <br>移除一名友方英雄身上的所有控制类减益效果(<keyword>滞空</keyword>和<keyword>压制</keyword>除外)并为其回复<healing>100 - 250生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": ["1028", "3067", "3114"], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health", "ManaRegen", "Tenacity"], "sell": "1960"}, {"itemId": "323504", "name": "炽热香炉", "price": "700", "total": "2600", "description": "<mainText><stats><attention>55</attention>法术强度<br><attention> 10%</attention>治疗和护盾强度<br><attention>150%</attention>基础法力回复<br><attention>6%</attention>移动速度</stats><br><br><passive>圣洁化</passive><br>对一名友方英雄施加治疗或护盾时，会使你们两个都获得持续6秒的强化，提供<attackSpeed>25%攻击速度</attackSpeed>和<magicDamage>20魔法伤害</magicDamage> <OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "", "into": [], "from": ["1052", "3113", "3114"], "types": ["AttackSpeed", "ManaRegen", "NonbootsMovement", "SpellDamage"], "sell": "1820"}, {"itemId": "324005", "name": "帝国指令", "price": "600", "total": "2750", "description": "<mainText><stats><attention>65</attention>法术强度<br><attention>20</attention>技能急速<br><attention>150%</attention>基础法力回复</stats><br><br><passive>协同开火</passive> 每个目标 (0秒)<br><keyword>减速</keyword>或<keyword>定身</keyword>一名英雄时会将其标记5秒。友方英雄的伤害会引爆标记，造成<magicDamage>相当于10%当前生命值的魔法伤害</magicDamage>。 </mainText>", "item_desc": "", "into": [], "from": ["1052", "3108", "4642"], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "1925"}, {"itemId": "326616", "name": "流水法杖", "price": "750", "total": "2600", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention> 10%</attention>治疗和护盾强度<br><attention>150%</attention>基础法力回复<br><attention>15</attention>技能急速</stats><br><br><passive>湍流</passive><br>当你为一名友军提供治疗或护盾效果时，你和你的目标都会获得持续6秒的<magicDamage>45法术强度</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": ["1052", "3108", "3114"], "types": ["AbilityHaste", "CooldownReduction", "ManaRegen", "SpellDamage"], "sell": "1820"}, {"itemId": "326617", "name": "月石再生器", "price": "800", "total": "2900", "description": "<mainText><stats><attention>35</attention>法术强度<br><attention>400</attention>生命值<br><attention>20</attention>技能急速<br><attention>150%</attention>基础法力回复</stats><br><br><passive>星光恩典</passive><br>对一名友军的治疗或护盾会连锁至另一名友方英雄(你自己除外)，提供相当于<healing> 30%</healing>原治疗值的治疗效果或<shield> 35%</shield>原护盾值的护盾效果。</mainText>", "item_desc": "", "into": [], "from": ["1028", "3067", "4642"], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen", "SpellDamage"], "sell": "2030"}, {"itemId": "326620", "name": "海力亚的回响", "price": "900", "total": "2600", "description": "<mainText><stats><attention>35</attention>法术强度<br><attention>250</attention>生命值<br><attention>20</attention>技能急速<br><attention>150%</attention>基础法力回复</stats><br><br><passive>灵魂虹吸</passive><br>对一名英雄造成伤害时会提供一块<passive>灵魂碎片</passive>，至多至2块。对一名友军提供治疗或护盾时，会消耗所有<passive>灵魂碎片</passive>来回复<healing>生命值</healing>并对相距最近的那个敌方英雄造成基于碎片数量的<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": ["3067", "4642"], "types": ["AbilityHaste", "CooldownReduction", "Health", "ManaRegen", "SpellDamage"], "sell": "1820"}, {"itemId": "326621", "name": "黎明核心", "price": "850", "total": "2900", "description": "<mainText><stats><attention>50</attention>法术强度<br><attention> 20%</attention>治疗和护盾强度<br><attention>150%</attention>基础法力回复</stats><br><br><passive>最初之光</passive><br>每100%基础法力回复获得<healing>2%治疗和护盾强度</healing>和<scaleAP>10法术强度</scaleAP>。</mainText>", "item_desc": "", "into": [], "from": ["1026", "3114", "3114"], "types": ["ManaRegen", "SpellDamage"], "sell": "2030"}, {"itemId": "326657", "name": "时光之杖", "price": "450", "total": "2600", "description": "<mainText><stats><attention>45</attention>法术强度<br><attention>350</attention>生命值<br><attention> 400</attention>法力</stats><br><br><passive>时无限</passive><br>这件装备每60秒获得<scaleHealth>10生命值</scaleHealth>、<scaleMana>20法力值</scaleMana>和<scaleAP>3法术强度</scaleAP>，至多至10层。在达到最大层数时，还会使英雄等级提升1级。<br><br><passive>永恒</passive><br>承受来自英雄的伤害时，回复相当于<scaleMana>10%</scaleMana>伤害值的<scaleMana>法力值</scaleMana>。<br>施放一个技能时，<healing>为自身治疗相当于25%法力消耗的生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": ["1026", "3803"], "types": ["Health", "HealthRegen", "<PERSON><PERSON>", "ManaRegen", "SpellDamage"], "sell": "1820"}, {"itemId": "328020", "name": "深渊面具", "price": "1200", "total": "2850", "description": "<mainText><stats><attention>350</attention>生命值<br><attention>50</attention>魔法抗性<br><attention>15</attention>技能急速</stats><br><br><passive>损毁</passive><br>使附近的敌方英雄承受12%额外<magicDamage>魔法伤害</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": ["1057", "3067"], "types": ["AbilityHaste", "CooldownReduction", "Health", "MagicResist", "SpellBlock"], "sell": "1995"}, {"itemId": "443054", "name": "暗钢利爪", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>50%</attention>攻击速度<br><attention>55</attention>护甲<br><attention>10%</attention>移动速度</stats><br><br><passive>砍伤</passive><br>攻击附带真实伤害 。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "AttackSpeed", "NonbootsMovement"], "sell": "400"}, {"itemId": "443055", "name": "爆鸣", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>45%</attention>攻击速度<br><attention>15%</attention>移动速度</stats><br><br><passive>极性</passive><br>攻击时，如果该目标不同于你最近一次触发过<keyword>盈能攻击</keyword>的目标，则让盈能准备就绪。<br><br><passive>电机</passive><br><keyword>盈能攻击</keyword>会造成额外的魔法伤害，伤害值基于<magicDamage>目标的当前生命值</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "NonbootsMovement"], "sell": "400"}, {"itemId": "443056", "name": "魔王之冕", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br><passive>至高无上</passive><br>使你的生命值、护甲、魔法抗性、攻击力、法术强度、攻击速度和技能急速提升26%，这个数值在获得此装备后的每次回合胜利时提升0%并在每次回合失败后降低-3%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "AttackSpeed", "Damage", "Health", "MagicResist", "SpellDamage"], "sell": "400"}, {"itemId": "443058", "name": "熔石之盾", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>100</attention>护甲</stats><br><br><passive>岿然如大地</passive><br>使你的护甲提升20%，并获得基于你护甲的格挡几率。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "Health"], "sell": "400"}, {"itemId": "443059", "name": "星夜斗篷", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>300</attention>生命值<br><attention>100</attention>魔法抗性</stats><br><br><passive>浩渺如群星</passive><br>使你的<scaleMR>魔法抗性</scaleMR>提升20%。使你所受的来自非普攻来源的伤害降低一定百分比，这个百分比受益于你的<scaleMR>魔法抗性</scaleMR>，至多至50%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "SpellBlock"], "sell": "400"}, {"itemId": "443060", "name": "神圣之剑", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 110</attention>适应之力<br><attention>50%</attention>暴击几率</stats><br><br><passive>严厉斥责</passive><br>每次暴击造成随机的<keyword>额外暴击伤害</keyword>，至多受益于你的50%暴击几率。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["CriticalStrike", "Damage", "SpellDamage"], "sell": "400"}, {"itemId": "443061", "name": "熵之力", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>900</attention>生命值<br><attention>30</attention>技能急速<br><attention>25%</attention>暴击几率</stats><br><br><passive>萎缩</passive><br>你施加的定身类控制效果会使用你的<keyword>暴击几率</keyword>，来使其持续时长提升0.25 +33%秒。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "CriticalStrike", "Health", "SpellBlock"], "sell": "400"}, {"itemId": "443062", "name": "血色赠礼", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>20</attention>技能急速<br><attention> 15%</attention>治疗和护盾强度</stats><br><br><passive>资助</passive><br>将你对敌人造成的15%总伤害储存起来。每当这个储量超过333时，就会消耗它来治疗你自身和你的友军，治疗量相当于该储量。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "SpellDamage", "SpellVamp"], "sell": "400"}, {"itemId": "443063", "name": "艾莉莎的奇迹", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>50</attention>护甲<br><attention>50</attention>魔法抗性<br><attention>25</attention>技能急速</stats><br><br><passive>坚忍活力</passive><br>每损失100生命值就会获得+ 2.5%治疗和护盾强度，至多至60%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "SpellBlock"], "sell": "400"}, {"itemId": "443064", "name": "飞升护符", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br><attention> ?</attention>攻击力<br><attention> ?</attention>攻击速度<br><attention> ?</attention>暴击几率<br><attention> ?</attention>暴击伤害<br><br><attention> ?</attention>法术强度<br><attention> ?</attention>技能急速<br><br><attention> ?</attention>生命值<br><attention> ?%</attention> 基础生命回复<br><attention> ?</attention>法力值<br><attention> ?%</attention> 基础法力回复<br><attention> ?</attention>护甲<br><attention> ?</attention>魔法抗性<br><br><attention> ? || ?%</attention> 穿甲和护甲穿透<br><attention> ? || ?%</attention> 法术穿透<br><attention> ?%</attention> 生命偷取<br><attention> ?%</attention> 全能吸血<br><attention> ? || ?%</attention> 移动速度<br><attention> ?%</attention> 治疗和护盾强度<br><br><br> <active>主动</active><br><active>交叠</active> 重新刷新【飞升护符】的各个属性。你每次这么做时，这些属性就会变得更强。 每回合两次 (在有【尖端发明家】时为三次)。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "ArmorPenetration", "AttackSpeed", "CooldownReduction", "CriticalStrike", "Damage", "Health", "HealthRegen", "LifeSteal", "MagicPenetration", "MagicResist", "<PERSON><PERSON>", "ManaRegen", "NonbootsMovement", "SpellBlock", "SpellDamage", "SpellVamp"], "sell": "400"}, {"itemId": "443069", "name": "断筋者", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>45</attention>攻击力<br><attention>40%</attention>攻击速度<br><attention>25%</attention>暴击几率</stats><br><br><passive>冲洗</passive><br>暴击时：<li> 施加一层流血效果，在2秒里持续造成25%本次造成伤害值 + 0物理伤害。这个效果可以叠加任意次。<li> 施加一层持续2秒的7%减速。这个效果至多可叠加至35%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "CriticalStrike", "Damage"], "sell": "400"}, {"itemId": "443079", "name": "涡轮炼金罐", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>600</attention>生命值<br><attention> 80</attention>适应之力</stats><br><br><br><br> <active>主动</active>  (0秒)<br><active>极限超载</active><br>移除所有控制类减益效果(<status>击飞</status>除外)并进入持续3秒的不可阻挡状态。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "Health", "NonbootsMovement"], "sell": "400"}, {"itemId": "443080", "name": "双生面具", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br><passive>一致</passive><br>获得你队友的一部分生命值、护甲、魔抗、攻击力、法术强度、攻击速度、以及技能急速，在队友也有<keywordMajor>双生面具</keywordMajor>并且还活着时，这些属性会显著提升。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "AttackSpeed", "Damage", "Health", "MagicResist", "SpellDamage"], "sell": "400"}, {"itemId": "443081", "name": "海克斯弹丸配枪", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>75%</attention>攻击速度<br><attention>500</attention>生命值</stats><br><br><passive>掩护射击</passive><br>在你友军附近时，<OnHit>攻击特效</OnHit>获得一层【指令】，在1层时，你下一次施加一个<OnHit>攻击特效</OnHit>会使你的队友也对目标发射一颗弹丸，造成<physicalDamage>0物理伤害</physicalDamage>并施加该队友的<OnHit>攻击特效</OnHit>。<br><br><keywordMajor>掩护射击</keywordMajor>无法触发<keywordMajor>掩护射击</keywordMajor>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Health"], "sell": "400"}, {"itemId": "443083", "name": "狂徒铠甲", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>1100</attention>生命值<br><attention>4%</attention>移动速度</stats><br><br><passive>狂徒之心</passive><br>需要1350<healing>额外生命值</healing>。<br><br>每秒回复<healing>0生命值</healing>。如果你在4秒内没有受到伤害，就会每秒回复额外的<healing>0生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "HealthRegen", "NonbootsMovement"], "sell": "400"}, {"itemId": "443090", "name": "收割者的过路费", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 40</attention>适应之力<br><attention>50%</attention>攻击速度<br><attention>10%</attention>移动速度</stats><br><br><passive>播种</passive><br>使你来自所有来源的攻击速度提升25%。<br><br><passive>收割</passive><br><OnHit>攻击特效</OnHit>，造成<trueDamage>最大生命值的真实伤害</trueDamage>，并使目标降低相当于造成伤害的最大生命值，持续至回合结束。对相同敌人的后续命中会使这个数额提升0.1%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "NonbootsMovement", "OnHit", "SpellDamage"], "sell": "400"}, {"itemId": "443193", "name": "石像鬼石板甲", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>65</attention>护甲<br><attention>65</attention>魔法抗性<br><attention>15</attention>技能急速<br><attention>10%</attention>移动速度</stats><br><br><active>主动 - </active><active>坚不可摧：</active>获得持续衰减的护盾值和体型提升。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "Armor", "CooldownReduction", "SpellBlock"], "sell": "400"}, {"itemId": "444636", "name": "暗夜收割者", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention>300</attention>生命值<br><attention>25</attention>技能急速</stats><br><br><passive>夺魂</passive> 每个英雄 (0秒)<br>用攻击或技能对一名英雄造成伤害时会造成额外的<magicDamage>0魔法伤害</magicDamage>并为你提供<speed>40%移动速度</speed>，持续1.5秒。<br><br><rules>对一名未受影响的英雄造成伤害时会延长移动速度加成的持续时间。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "CooldownReduction", "Health", "NonbootsMovement", "SpellDamage"], "sell": "400"}, {"itemId": "444637", "name": "恶魔之拥", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>700</attention>生命值</stats><br><br><passive>不祥契约</passive><br>每100已损失生命值就会获得+1.5%<scaleAP>法术强度</scaleAP>和1.5%移动速度，至多至45%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "SpellDamage"], "sell": "400"}, {"itemId": "444644", "name": "破碎王后之冕", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>85</attention>法术强度<br><attention>300</attention>生命值<br><attention> 600</attention>法力<br><attention>25</attention>技能急速</stats><br><br><passive>护卫</passive><br>你已被<keywordMajor>护卫</keywordMajor>，可使即将到来的英雄伤害降低40%。<keywordMajor>护卫</keywordMajor>在受到来自英雄的伤害后可存留3秒。 (0秒)。<br><br><rules>如果在此装备冷却完毕之前受到来自英雄的伤害，那么它的冷却时间会重新开始计算。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Health", "<PERSON><PERSON>", "SpellDamage"], "sell": "400"}, {"itemId": "446632", "name": "神圣分离者", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>55</attention>攻击力<br><attention>350</attention>生命值<br><attention>20</attention>技能急速</stats><br><br><passive>咒刃</passive><br>施放技能后，你的下一次普通攻击因强化而附带额外伤害。如果目标是一个英雄，那么还会治疗自身。<br><br></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage", "Health", "MagicPenetration", "OnHit"], "sell": "400"}, {"itemId": "446656", "name": "永霜", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>100</attention>法术强度<br><attention>250</attention>生命值<br><attention> 600</attention>法力<br><attention>25</attention>技能急速</stats><br><br> <active>主动</active>  (0秒)<br><active>冰川覆盖</active><br>在一个锥形范围内造成伤害，使命中的敌人<status>减速</status>。锥形范围中心的敌人会转而被<status>禁锢</status>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Active", "CooldownReduction", "Health", "<PERSON><PERSON>", "Slow", "SpellDamage"], "sell": "400"}, {"itemId": "446667", "name": "辉耀美德", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>400</attention>生命值<br><attention>35</attention>护甲<br><attention>35</attention>魔法抗性<br><attention> 12%</attention>治疗和护盾强度</stats><br><br><passive>指引之光</passive><br>在你施放你的终极技能后，你会进入<status>超凡入圣</status>状态，使你的<scaleHealth>最大生命值</scaleHealth>提升<scaleHealth>0</scaleHealth>，持续9秒。 在<status>超凡入圣</status>状态下，你和你1200码内的友军们会在持续期间获得<scaleHealth>0 ()</scaleHealth>治疗效果  (0秒)。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "<PERSON>ra", "CooldownReduction", "Health", "SpellBlock"], "sell": "400"}, {"itemId": "446671", "name": "狂风之力", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>65</attention>攻击力<br><attention>30%</attention>攻击速度<br><attention>25%</attention>暴击几率<br><attention>12%</attention>移动速度</stats><br><br> <active>暴雨 II</active> <br>朝着目标方向冲刺并翻越地形，同时对你终点附近生命值最低的敌人发射3个弹体。造成物理伤害，对低生命值的目标时造成提升过的伤害。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Active", "AttackSpeed", "CriticalStrike", "Damage", "NonbootsMovement"], "sell": "400"}, {"itemId": "446691", "name": "德拉克萨的暮刃", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>50</attention>攻击力<br><attention>20</attention>穿甲<br><attention>20</attention>技能急速</stats><br><br><passive>夜行者</passive><br>你的技能可基于目标的已损失生命值造成额外的一部分伤害。如果一名在过去3秒内曾被你造成过伤害的英雄阵亡，你就会进入持续1.5秒的对非建筑单位的<keywordStealth>不可被选取</keywordStealth>状态 (0秒)。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage", "Slow", "Stealth"], "sell": "400"}, {"itemId": "446693", "name": "暗行者之爪", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>20</attention>穿甲<br><attention>20</attention>技能急速</stats><br><br><active>沙之挥击</active><br>冲刺穿过目标敌人，造成目标一部分<scaleHealth>最大生命值</scaleHealth>的<physicalDamage>额外物理伤害</physicalDamage>。接下来的3秒里，你对目标造成的伤害提升。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ArmorPenetration", "CooldownReduction", "Damage"], "sell": "400"}, {"itemId": "447100", "name": "幻境之刃", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 65</attention>适应之力<br><attention>60%</attention>攻击速度<br><attention>12%</attention>移动速度</stats><br><br><passive>模糊</passive><br>你的攻击会使你的<status>冲刺</status>和<status>闪烁</status>技能的剩余冷却时间缩短12%(对终极技能来说，降低至4%)。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "AttackSpeed", "Damage", "NonbootsMovement", "SpellDamage"], "sell": "400"}, {"itemId": "447101", "name": "投机者之刃", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>70%</attention>攻击速度<br><attention>40</attention>技能急速<br><attention>8%</attention>移动速度</stats><br><br><passive>银行存款</passive><br>你的攻击和技能在命中一个敌人时，有12%几率储存30到245金币。在回合获胜时，提取出来。在回合失败时，你的存款损失25%。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "AttackSpeed", "NonbootsMovement"], "sell": "400"}, {"itemId": "447102", "name": "实界裂缝", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>40%</attention>攻击速度<br><attention>300</attention>生命值</stats><br><br><passive>兹若特</passive>  (0秒)<br>在攻击或用技能对一个敌人造成伤害时，召唤8个虚空巢虫去攻击该目标。虚空巢虫造成<magicDamage>0魔法伤害</magicDamage>并且存活至多3秒。<br><br>在你阵亡时，生成6个虚空巢虫。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Health", "SpellDamage"], "sell": "400"}, {"itemId": "447103", "name": "血术师之盔", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>30</attention>技能急速<br><attention> 15%</attention>全能吸血</stats><br><br><passive>绯红忠诚</passive><br>阈值30%生命偷取/全能吸血：获得<healing>500最大生命值</healing>。汲取附近敌人所受的10%全部伤害。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Damage", "SpellVamp"], "sell": "400"}, {"itemId": "447104", "name": "激发之匣", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention>20</attention>技能急速<br><attention>200</attention>生命值</stats><br><br><passive>充实灵魂</passive><br>在你800码内的任一技能施放都会为你提供一层充能。<br><br>在30层充能时，获得<shield>0护盾值</shield>、<scaleAP>0法术强度</scaleAP>和<speed>0移动速度</speed>，持续到回合结束。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Health", "SpellDamage"], "sell": "400"}, {"itemId": "447105", "name": "至高天诺言", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>70</attention>法术强度<br><attention> 18%</attention>治疗和护盾强度<br><attention>30</attention>技能急速<br><attention>125%</attention>基础法力回复</stats><br><br> <active>主动</active> (0秒)<br><active>警惕</active><br>传送至你的友军并在着陆时提供持续5秒的<shield>0护盾值</shield>。可在友军处于濒死状态时使用。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ManaRegen", "SpellDamage"], "sell": "400"}, {"itemId": "447106", "name": "龙心", "price": "1000", "total": "1000", "description": "<mainText><stats></stats><br><br><passive>心灵烈焰</passive><br>使你的<scaleHealth>生命值</scaleHealth>、<scaleArmor>护甲</scaleArmor>、<scaleMR>魔法抗性</scaleMR>、<physicalDamage>攻击力</physicalDamage>、 <magicDamage>法术强度</magicDamage>、<attackSpeed>攻击速度</attackSpeed>和技能急速提升，提升数额为4%x你持有的龙魂数量。<br><br>每2个回合，获得一个龙魂。<br><br>如果你已经有了每一种元素龙魂并且即将获得又一个，那么体内就会觉醒一股上古威能……<br><br><rules>在购买时，如果已经过了第5回合，那么会即刻获得1个龙魂，并且第5回合后的每2个回合都会提供1个额外的龙魂。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "AttackSpeed", "Damage", "Health", "HealthRegen", "MagicResist"], "sell": "400"}, {"itemId": "447107", "name": "斩首者", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 80</attention>适应之力<br><attention>50%</attention>攻击速度<br><attention>12%</attention>移动速度</stats><br><br>攻击和非终极技能会提供层数。每层提供终极技能伤害和终极技能急速。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage", "NonbootsMovement", "SpellDamage"], "sell": "400"}, {"itemId": "447108", "name": "符文雕刻者", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>20</attention>技能急速<br><attention>8%</attention>移动速度</stats><br><br><passive>螺旋</passive><br>移动、攻击以及用一个技能造成伤害时会提供30<keyword>盈能</keyword>层数并且在就绪时触发<keyword>盈能攻击</keyword>。<br><br><passive>螺旋式扩散</passive><br>在<keyword>盈能攻击</keyword>时，获得一个<keyword>符文</keyword>层数，持续到回合结束。随后每层<keyword>符文</keyword>对目标发射一个弹体，每个弹体造成<magicDamage>0魔法伤害</magicDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["NonbootsMovement", "SpellDamage"], "sell": "400"}, {"itemId": "447109", "name": "残忍", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>80</attention>法术强度<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性</stats><br><br><passive>观测星落</passive><br>在你对一名敌方英雄施加<status>定身</status>或<status>缚地</status>时，召唤一颗彗星至敌人上方。彗星会在1秒后着陆，在区域内造成额外的<magicDamage>0魔法伤害</magicDamage>。这个效果对每次技能施放和每个目标都有6秒冷却时间。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "MagicResist", "SpellDamage"], "sell": "400"}, {"itemId": "447110", "name": "月华咒刃", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>85</attention>法术强度<br><attention>400</attention>生命值<br><attention>30%</attention>韧性</stats><br><br><passive>无情</passive><br>在你使用一次技能时，重置你的普攻计时器并且你接下来的2次攻击获得<attackSpeed>90%攻击速度</attackSpeed>。<br><br>在你攻击时，你的各技能冷却时间缩短0.5秒。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Health", "SpellDamage", "Tenacity"], "sell": "400"}, {"itemId": "447111", "name": "霸王血铠", "price": "2500", "total": "2500", "description": "<mainText><stats><attention>40</attention>攻击力<br><attention>400</attention>生命值</stats><br><br><passive>专横</passive><br>获得相当于你3%<scaleHealth>额外生命值</scaleHealth>的<physicalDamage>0攻击力</physicalDamage>。<br><br><passive>报复</passive><br>获得基于你的百分比已损失生命值的<physicalDamage>15%攻击力提升</physicalDamage>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health"], "sell": "1250"}, {"itemId": "447112", "name": "食肉斧剑", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 70</attention>适应之力<br><attention>500</attention>生命值</stats><br><br><passive>切肉</passive><br>造成伤害时会击碎3<scaleArmor>护甲</scaleArmor>和<scaleMR>魔法抗性</scaleMR>，持续5秒，可叠加至多10次。施加层数效果对每个技能有1秒冷却时间。<br><br><passive>食肉</passive><br>参与击杀英雄后，治疗你自身和你的友军，治疗值相当于<healing>目标的18%最大生命值</healing>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage", "MagicPenetration", "SpellDamage"], "sell": "400"}, {"itemId": "447113", "name": "爆炸之球", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>90</attention>法术强度<br><attention>12</attention>法术穿透<br><attention> 600</attention>法力<br><attention>20</attention>技能急速</stats><br><br><passive>炸弹</passive><br>技能伤害会标记目标，并储存20%已造成的伤害值(对被<keyword>定身</keyword>的敌人提升至25%)。<br><br>在你最后一次用技能对目标造成伤害的3秒后，引爆其身上的伤害储量。如果任一时间点上的伤害储量已足以击杀该目标，则会立刻引爆。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["MagicPenetration", "<PERSON><PERSON>", "SpellDamage"], "sell": "400"}, {"itemId": "447114", "name": "混响之刃", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>35</attention>护甲<br><attention>35</attention>魔法抗性<br><attention>40%</attention>攻击速度</stats><br><br><passive>混响</passive><br>战斗开始时，你每拥有<attackSpeed>10%额外攻速</attackSpeed>就会获得<healing>50最大生命值</healing>。<br><br><passive>共振</passive><br>普攻造成<magicDamage>0魔法伤害<OnHit>攻击特效</OnHit></magicDamage>。<br><br><passive>轰鸣</passive><br><status>定身</status>或<status>缚地</status>一名敌方英雄时提供25层效果，层数持续10秒。在100层时，<status>定身</status>一名敌方英雄将施加3次<OnHit>攻击特效</OnHit>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Armor", "AttackSpeed", "Health", "MagicResist", "OnHit"], "sell": "400"}, {"itemId": "447115", "name": "弑王", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>60</attention>攻击力<br><attention>15</attention>穿甲<br><attention>8%</attention>移动速度</stats><br><br><passive>终结王权</passive><br>在回合开始时，将生命值最低的那个敌人宣称为 <keywordMajor>摄政王</keywordMajor>。参与击杀<keywordMajor>摄政王</keywordMajor>会提供给你永久的<physicalDamage>10攻击力</physicalDamage>并刷新你的终极技能的冷却时间。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["ArmorPenetration", "Damage", "NonbootsMovement"], "sell": "400"}, {"itemId": "447116", "name": "均衡十手", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 70</attention>适应之力<br><attention>400</attention>生命值<br><attention>30</attention>技能急速</stats><br><br><passive>肋骨之间</passive><br>你可以看到附近敌方英雄们身上的弱点。通过弱点造成伤害时会造成额外的<trueDamage>0百分比最大生命值的真实伤害</trueDamage>并提供持续1.5秒的<speed>0移动速度</speed>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Damage", "Health", "SpellDamage"], "sell": "400"}, {"itemId": "447118", "name": "炎术师的披风", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 85</attention>适应之力<br><attention>400</attention>生命值<br><attention>15</attention>技能急速</stats><br><br><passive>火花</passive> (5秒)冷却时间<br>攻击或技能在命中一名敌方英雄时会<keywordMajor>灼烧</keywordMajor>该英雄，在3秒里持续造成<magicDamage>0魔法伤害</magicDamage>。<br><br><passive>洁化之焰</passive>每个英雄 (0秒)<br>给一个敌方英雄施加一个<keywordMajor>灼烧</keywordMajor>时会在附近生成一团持续5秒的<keywordMajor>烈焰</keywordMajor>。在处于一团<keywordMajor>烈焰</keywordMajor>中时，获得<speed>0移动速度</speed>并且每秒回复<healing>0生命值</healing>，其中的敌人们会每秒承受<magicDamage>0魔法伤害</magicDamage>。<br><br>每团<keywordMajor>烈焰</keywordMajor>的规模和强度受益于你拥有的独特<keywordMajor>灼烧</keywordMajor>来源的数量。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["Damage", "Health", "SpellDamage", "SpellVamp"], "sell": "400"}, {"itemId": "447119", "name": "闪电杖", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>500</attention>生命值<br><attention>30</attention>护甲<br><attention>30</attention>魔法抗性<br><attention>8%</attention>移动速度</stats><br><br><passive>召唤闪电</passive><br>每16秒，在你的上方<keywordMajor>自动施放</keywordMajor>一团雷云，雷云会在短暂延迟后发射一团闪电弹，造成<magicDamage>0外加10%最大生命值的魔法伤害</magicDamage>和持续2秒的30%<status>减速</status>。<br><br>如果你被这个闪电弹击中，则会获得持续2秒的<shield>0护盾值</shield>。<br><br><passive>全自动化</passive><br>你的<keywordMajor>自动施放</keywordMajor>冷却时间缩短1秒并受益于你的技能急速。<br><br><rules>【召唤闪电】的最小冷却时间为5秒。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage"], "sell": "400"}, {"itemId": "447120", "name": "钻头长矛", "price": "1000", "total": "1000", "description": "<mainText><stats><attention> 75</attention>适应之力<br><attention>30%</attention>攻击速度</stats><br><br><passive>长兵器</passive><br>使你的攻击距离提升75。<br><br><passive>百步穿杨</passive><br>基于你的目标与你相隔的距离，攻击至多造成30%额外伤害并且技能至多造成40%额外伤害。额外伤害在1000码时达到上限。 </mainText>", "item_desc": "", "into": [], "from": [], "types": ["AttackSpeed", "Damage"], "sell": "400"}, {"itemId": "447121", "name": "暮色之锋", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>70</attention>攻击力<br><attention>100</attention>法术强度</stats><br><br><passive>界间道路</passive><br>达到130额外攻击力和180法术强度阈值：飞轮刃环绕于你，对其击中的敌人们造成连续不断的伤害。<br><br><keywordMajor>精神世界</keywordMajor>：获得<scaleAP>法术强度</scaleAP>和技能急速。<br><keywordMajor>物质世界</keywordMajor>：获得<physicalDamage>攻击力</physicalDamage>和<attackSpeed>攻击速度</attackSpeed>。</mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "Armor", "AttackSpeed", "Damage", "Health", "MagicResist", "SpellDamage"], "sell": "400"}, {"itemId": "447122", "name": "黑洞护手", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>900</attention>生命值<br><attention>25</attention>技能急速</stats><br><br><passive>积聚</passive><br><OnHit>攻击特效</OnHit>，获得1层<keywordMajor>积聚</keywordMajor>，并且在<status>定身</status>一名敌方英雄时获得5层。这些层数会持续到回合结束，至多至50层。每层提升你2%体型。<br><br> <active>主动</active>  (0秒)<br><active>暗星</active><br>召唤一个黑洞，黑洞受益于你的体型。附近的敌人们<slow>减速</slow>35%并且那些在中心区域的敌人们会被拉拽得更近并且<magic>每秒承受0魔法伤害</magic>，而你则会获得1层 <keywordMajor>积聚</keywordMajor>。黑洞持续0秒。<br><br><rules>每10层提升一次体型。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "AttackSpeed", "OnHit", "SpellBlock"], "sell": "400"}, {"itemId": "447123", "name": "傀儡操纵器", "price": "1000", "total": "1000", "description": "<mainText><stats><attention>30%</attention>攻击速度<br><attention> 15%</attention>治疗和护盾强度<br><attention>40</attention>技能急速<br><attention>150%</attention>基础法力回复</stats><br><br><passive>拉线</passive> <br>攻击一个英雄时会添加持续15秒的一层效果。在4层时，将其<status>狂暴</status>2秒。你将敌人们陷入<status>狂暴</status>状态，并为其提供<attackSpeed>0攻击速度</attackSpeed>和<speed>40%移动速度</speed>。(25秒冷却时间)。<br><br>为你自身或一名友军施放一个<healing>治疗类</healing>、<shield>护盾类</shield>或增益类技能时，会使这个效果的冷却时间缩短5秒。<br><br><rules>如果仅有一名敌人存活，则转而对其造成相同持续时间的<status>恐惧</status>。</rules></mainText>", "item_desc": "", "into": [], "from": [], "types": ["AbilityHaste", "ManaRegen", "SpellDamage"], "sell": "400"}]}
<?php
// 读取原始JSON文件
$jsonData = file_get_contents('/home/<USER>/php/project/github-project/lol-zhuanbbei.json');
$data = json_decode($jsonData, true);

// 定义要保留的字段
$keepFields = [
    'itemId',
    'name',
    'price',
    'total',
    'description',
    'item_desc',
    'into',
    'from',
    'types',
    'sell'
];

// 处理每个装备项
$processedItems = [];
foreach ($data['items'] as $item) {
    $processedItem = [];
    foreach ($keepFields as $field) {
        if (isset($item[$field])) {
            $processedItem[$field] = $item[$field];
        }
    }
    $processedItems[] = $processedItem;
}

// 创建新的数据结构
$processedData = [
    'items' => $processedItems
];

// 保存处理后的JSON
$processedJson = json_encode($processedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
file_put_contents('/home/<USER>/php/project/github-project/processed_items.json', $processedJson);

echo "处理完成，共处理 " . count($processedItems) . " 个装备项。\n";
?>
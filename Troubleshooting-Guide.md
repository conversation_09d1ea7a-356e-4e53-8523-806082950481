# GitHub项目商业化分析系统 - 故障排除指南

## 概述

本指南提供了GitHub项目商业化分析系统常见问题的诊断和解决方案，帮助用户快速定位和解决系统运行中遇到的问题。

## 🔍 问题诊断流程

### 1. 快速健康检查

首先执行系统健康检查：

```bash
# 检查n8n服务状态
curl http://localhost:5678/webhook/system-status

# 检查工作流状态
curl http://localhost:5678/webhook/workflow-stats
```

### 2. 查看日志

```bash
# n8n主日志
tail -f ~/.n8n/logs/n8n.log

# 系统日志（Linux）
journalctl -u n8n -f

# Docker日志（如果使用Docker）
docker logs -f n8n-container
```

### 3. 检查资源使用

```bash
# 内存使用情况
free -h

# CPU使用情况
top -p $(pgrep -f n8n)

# 磁盘空间
df -h
```

## 🚨 常见问题及解决方案

### 1. GitHub API相关问题

#### 问题：API限流错误 (Rate Limit Exceeded)

**症状**：
- 错误代码：403
- 错误消息：`API rate limit exceeded`
- 工作流执行中断

**原因分析**：
- GitHub API调用频率超过限制
- 使用个人访问令牌的限额较低
- 多个并发任务同时调用API

**解决方案**：

1. **检查当前限额使用情况**：
```bash
curl -H "Authorization: token YOUR_TOKEN" \
  https://api.github.com/rate_limit
```

2. **升级到GitHub App认证**：
```javascript
// 在n8n中配置GitHub App凭据
{
  "authType": "app",
  "appId": "123456",
  "privateKey": "-----BEGIN RSA PRIVATE KEY-----...",
  "installationId": "789012"
}
```

3. **调整批处理参数**：
```javascript
// 在主工作流中减少批处理大小
{
  "batchSize": 1,  // 从3减少到1
  "rateLimitDelay": 5000  // 增加延迟到5秒
}
```

4. **实施智能重试**：
```javascript
// 配置指数退避重试
{
  "maxRetries": 5,
  "baseDelay": 60000,  // 1分钟基础延迟
  "maxDelay": 3600000  // 最大1小时延迟
}
```

#### 问题：认证失败 (Authentication Failed)

**症状**：
- 错误代码：401
- 错误消息：`Bad credentials`

**解决方案**：

1. **验证访问令牌**：
```bash
curl -H "Authorization: token YOUR_TOKEN" \
  https://api.github.com/user
```

2. **检查令牌权限**：
确保令牌具有以下权限：
- `public_repo` - 访问公共仓库
- `read:org` - 读取组织信息
- `read:user` - 读取用户信息

3. **更新n8n凭据**：
- 进入n8n Web界面
- 导航到 Settings > Credentials
- 更新GitHub API凭据

#### 问题：仓库不存在或无权访问 (Repository Not Found)

**症状**：
- 错误代码：404
- 错误消息：`Not Found`

**解决方案**：

1. **验证仓库名称格式**：
```javascript
// 正确格式：owner/repository
"facebook/react"  // ✓ 正确
"react"          // ✗ 错误
```

2. **检查仓库可见性**：
- 确认仓库是公开的
- 如果是私有仓库，确保令牌有访问权限

### 2. AI分析相关问题

#### 问题：OpenAI API调用失败

**症状**：
- 错误消息：`OpenAI API error`
- AI分析步骤失败

**解决方案**：

1. **检查API密钥**：
```bash
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer YOUR_API_KEY"
```

2. **验证账户余额**：
- 登录OpenAI控制台
- 检查账户余额和使用限额

3. **调整模型参数**：
```javascript
// 减少token使用量
{
  "model": "gpt-3.5-turbo",  // 使用更便宜的模型
  "max_tokens": 2000,       // 减少最大token数
  "temperature": 0.5        // 降低随机性
}
```

#### 问题：AI分析超时

**症状**：
- 错误消息：`Request timeout`
- 分析在AI步骤卡住

**解决方案**：

1. **增加超时时间**：
```javascript
{
  "timeout": 120000,  // 增加到2分钟
  "retries": 3
}
```

2. **优化提示词长度**：
```javascript
// 限制README内容长度
readme_preview: readme.substring(0, 1500)  // 从2000减少到1500
```

3. **分批处理分析**：
```javascript
// 将三个AI分析步骤串行执行而不是并行
```

### 3. 系统性能问题

#### 问题：内存不足 (Out of Memory)

**症状**：
- 错误消息：`JavaScript heap out of memory`
- 工作流执行中断
- 系统响应缓慢

**解决方案**：

1. **增加Node.js内存限制**：
```bash
# 启动n8n时设置内存限制
NODE_OPTIONS="--max-old-space-size=4096" n8n start
```

2. **优化数据处理**：
```javascript
// 减少批处理大小
{
  "batchSize": 2,  // 从5减少到2
  "clearDataBetweenBatches": true
}
```

3. **清理临时数据**：
```javascript
// 在处理完每个项目后清理数据
delete project.readme_full;
delete project.raw_contributors;
```

#### 问题：CPU使用率过高

**症状**：
- 系统响应缓慢
- CPU使用率持续100%

**解决方案**：

1. **限制并发任务**：
```javascript
{
  "maxConcurrentExecutions": 2,  // 限制并发执行数
  "executionTimeout": 1800       // 30分钟超时
}
```

2. **添加处理延迟**：
```javascript
// 在批处理间添加延迟
{
  "delayBetweenBatches": 3000  // 3秒延迟
}
```

### 4. 网络连接问题

#### 问题：网络超时 (Network Timeout)

**症状**：
- 错误消息：`ECONNRESET` 或 `ETIMEDOUT`
- 间歇性连接失败

**解决方案**：

1. **检查网络连接**：
```bash
# 测试GitHub API连接
curl -I https://api.github.com

# 测试OpenAI API连接
curl -I https://api.openai.com
```

2. **配置代理（如果需要）**：
```bash
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

3. **增加重试机制**：
```javascript
{
  "retries": 5,
  "retryDelay": 2000,
  "exponentialBackoff": true
}
```

### 5. 数据格式问题

#### 问题：JSON解析错误

**症状**：
- 错误消息：`Unexpected token in JSON`
- 数据处理步骤失败

**解决方案**：

1. **添加数据验证**：
```javascript
// 验证JSON格式
try {
  const data = JSON.parse(response);
  return data;
} catch (error) {
  console.error('JSON解析失败:', error);
  return { error: 'Invalid JSON format' };
}
```

2. **处理特殊字符**：
```javascript
// 清理特殊字符
const cleanText = text.replace(/[\x00-\x1F\x7F]/g, '');
```

### 6. 工作流执行问题

#### 问题：工作流卡住不动

**症状**：
- 执行状态显示"运行中"但无进展
- 长时间无响应

**解决方案**：

1. **检查执行历史**：
- 在n8n界面查看执行详情
- 识别卡住的具体节点

2. **手动停止并重启**：
```bash
# 重启n8n服务
sudo systemctl restart n8n

# 或者杀死进程重启
pkill -f n8n
n8n start
```

3. **清理执行队列**：
```javascript
// 在n8n设置中清理待执行的任务
```

## 🔧 预防性维护

### 1. 定期监控

设置监控脚本：

```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

# 检查n8n进程
if ! pgrep -f "n8n" > /dev/null; then
    echo "n8n进程未运行，正在重启..."
    n8n start &
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    echo "内存使用率过高: ${MEMORY_USAGE}%"
fi

# 检查磁盘空间
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 85 ]; then
    echo "磁盘使用率过高: ${DISK_USAGE}%"
fi
```

### 2. 日志轮转

配置日志轮转：

```bash
# /etc/logrotate.d/n8n
/home/<USER>/.n8n/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 user user
}
```

### 3. 备份策略

```bash
#!/bin/bash
# backup.sh - 备份脚本

BACKUP_DIR="/backup/n8n"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份工作流
cp -r ~/.n8n/workflows "$BACKUP_DIR/workflows_$DATE"

# 备份凭据
cp -r ~/.n8n/credentials "$BACKUP_DIR/credentials_$DATE"

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*_*" -mtime +7 -delete
```

## 📞 获取帮助

### 1. 收集诊断信息

在寻求帮助前，请收集以下信息：

```bash
# 系统信息收集脚本
echo "=== 系统信息 ==="
uname -a
echo ""

echo "=== n8n版本 ==="
n8n --version
echo ""

echo "=== Node.js版本 ==="
node --version
echo ""

echo "=== 内存使用 ==="
free -h
echo ""

echo "=== 磁盘使用 ==="
df -h
echo ""

echo "=== 最近错误日志 ==="
tail -50 ~/.n8n/logs/n8n.log | grep -i error
```

### 2. 联系支持

- **邮箱**: <EMAIL>
- **GitHub Issues**: https://github.com/your-org/github-analyzer/issues
- **文档**: https://docs.github-analyzer.com

### 3. 社区资源

- **用户论坛**: https://community.github-analyzer.com
- **FAQ**: https://docs.github-analyzer.com/faq
- **视频教程**: https://youtube.com/github-analyzer

## 📋 故障排除检查清单

### 启动问题
- [ ] n8n服务是否正在运行
- [ ] 端口5678是否被占用
- [ ] 环境变量是否正确设置
- [ ] 依赖包是否完整安装

### API问题
- [ ] GitHub API令牌是否有效
- [ ] OpenAI API密钥是否正确
- [ ] API限额是否充足
- [ ] 网络连接是否正常

### 性能问题
- [ ] 内存使用是否正常
- [ ] CPU使用率是否合理
- [ ] 磁盘空间是否充足
- [ ] 批处理大小是否合适

### 数据问题
- [ ] 输入数据格式是否正确
- [ ] 输出数据是否完整
- [ ] 字符编码是否正确
- [ ] JSON格式是否有效

---

**版本**: v1.0.0  
**更新时间**: 2024-07-18  
**维护者**: GitHub项目分析团队

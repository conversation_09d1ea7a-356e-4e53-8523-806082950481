{"name": "github-project-analysis-system", "version": "1.0.0", "description": "基于n8n构建的GitHub开源项目商业化分析系统", "main": "test-system.js", "scripts": {"test": "node test-system.js", "test:full": "node test-system.js --full", "test:performance": "node test-system.js --performance", "test:api": "node test-system.js --api-only", "start": "n8n start", "dev": "n8n start --tunnel", "setup": "npm install && npm run setup:workflows", "setup:workflows": "echo '请手动导入工作流文件到n8n'", "monitor": "node scripts/monitor.js", "backup": "node scripts/backup.js", "health-check": "curl -f http://localhost:5678/webhook/system-status || exit 1"}, "keywords": ["n8n", "github", "analysis", "automation", "ai", "business-intelligence", "open-source", "workflow"], "author": "GitHub项目分析团队", "license": "MIT", "dependencies": {"axios": "^1.6.0", "n8n": "^1.102.4"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.57.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/github-project-analysis-system.git"}, "bugs": {"url": "https://github.com/your-org/github-project-analysis-system/issues"}, "homepage": "https://github.com/your-org/github-project-analysis-system#readme", "n8n": {"workflows": ["workflows/main-analysis-workflow.json", "workflows/error-handling-workflow.json", "workflows/progress-monitoring-workflow.json", "workflows/report-generator-workflow.json"], "credentials": ["github-api", "openai-api"]}, "config": {"n8n": {"port": 5678, "host": "localhost", "protocol": "http"}, "github": {"api_url": "https://api.github.com", "rate_limit": 5000}, "openai": {"api_url": "https://api.openai.com/v1", "model": "gpt-4"}}}
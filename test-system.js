#!/usr/bin/env node

/**
 * GitHub项目商业化分析系统 - 端到端测试脚本
 * 
 * 此脚本用于验证整个系统的功能完整性和性能表现
 * 
 * 使用方法:
 * node test-system.js [--full] [--performance] [--api-only]
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class SystemTester {
    constructor(baseUrl = 'http://localhost:5678') {
        this.baseUrl = baseUrl;
        this.testResults = [];
        this.startTime = Date.now();
    }

    // 记录测试结果
    logResult(testName, success, message, duration = 0, details = {}) {
        const result = {
            test: testName,
            success,
            message,
            duration,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        const status = success ? '✅ PASS' : '❌ FAIL';
        const time = duration > 0 ? ` (${duration}ms)` : '';
        console.log(`${status} ${testName}${time}: ${message}`);
        
        if (!success && details.error) {
            console.log(`   Error: ${details.error}`);
        }
    }

    // 系统健康检查
    async testSystemHealth() {
        console.log('\n🔍 系统健康检查...');
        
        try {
            const start = Date.now();
            const response = await axios.get(`${this.baseUrl}/webhook/system-status`, {
                timeout: 10000
            });
            const duration = Date.now() - start;
            
            if (response.data.success && response.data.system_status === 'HEALTHY') {
                this.logResult(
                    '系统健康检查',
                    true,
                    '系统运行正常',
                    duration,
                    { status: response.data.system_status }
                );
                return true;
            } else {
                this.logResult(
                    '系统健康检查',
                    false,
                    '系统状态异常',
                    duration,
                    { status: response.data.system_status }
                );
                return false;
            }
        } catch (error) {
            this.logResult(
                '系统健康检查',
                false,
                '无法连接到系统',
                0,
                { error: error.message }
            );
            return false;
        }
    }

    // 测试GitHub API连接
    async testGitHubAPI() {
        console.log('\n🐙 GitHub API连接测试...');
        
        try {
            // 这里应该调用一个测试端点来验证GitHub API
            const start = Date.now();
            const response = await axios.post(`${this.baseUrl}/webhook/test-github-api`, {
                test_repo: 'facebook/react'
            }, { timeout: 30000 });
            const duration = Date.now() - start;
            
            if (response.data.success) {
                this.logResult(
                    'GitHub API连接',
                    true,
                    'GitHub API连接正常',
                    duration,
                    { rate_limit: response.data.rate_limit_remaining }
                );
                return true;
            } else {
                this.logResult(
                    'GitHub API连接',
                    false,
                    'GitHub API连接失败',
                    duration,
                    { error: response.data.error }
                );
                return false;
            }
        } catch (error) {
            this.logResult(
                'GitHub API连接',
                false,
                'GitHub API测试失败',
                0,
                { error: error.message }
            );
            return false;
        }
    }

    // 测试AI分析功能
    async testAIAnalysis() {
        console.log('\n🤖 AI分析功能测试...');
        
        try {
            const testProject = {
                name: 'test-project',
                description: 'A test project for validation',
                language: 'JavaScript',
                stars: 1000,
                forks: 200,
                contributors: 50
            };
            
            const start = Date.now();
            const response = await axios.post(`${this.baseUrl}/webhook/test-ai-analysis`, {
                project: testProject
            }, { timeout: 60000 });
            const duration = Date.now() - start;
            
            if (response.data.success && response.data.analysis) {
                const analysis = response.data.analysis;
                const hasAllScores = analysis.tech_score && analysis.business_score && analysis.risk_score;
                
                this.logResult(
                    'AI分析功能',
                    hasAllScores,
                    hasAllScores ? 'AI分析功能正常' : 'AI分析结果不完整',
                    duration,
                    { 
                        tech_score: analysis.tech_score,
                        business_score: analysis.business_score,
                        risk_score: analysis.risk_score
                    }
                );
                return hasAllScores;
            } else {
                this.logResult(
                    'AI分析功能',
                    false,
                    'AI分析失败',
                    duration,
                    { error: response.data.error }
                );
                return false;
            }
        } catch (error) {
            this.logResult(
                'AI分析功能',
                false,
                'AI分析测试失败',
                0,
                { error: error.message }
            );
            return false;
        }
    }

    // 测试完整工作流
    async testCompleteWorkflow() {
        console.log('\n⚙️ 完整工作流测试...');
        
        try {
            // 启动一个小规模的分析任务
            const analysisRequest = {
                languages: ['javascript'],
                sortOrder: 'stars',
                limit: 3,
                minStars: 10000,
                activity: '-1m',
                licenses: ['mit']
            };
            
            const start = Date.now();
            
            // 提交分析任务
            const submitResponse = await axios.post(
                `${this.baseUrl}/form/github-analysis`,
                analysisRequest,
                { timeout: 30000 }
            );
            
            if (!submitResponse.data.success) {
                this.logResult(
                    '完整工作流测试',
                    false,
                    '任务提交失败',
                    0,
                    { error: submitResponse.data.error }
                );
                return false;
            }
            
            const taskId = submitResponse.data.task_id;
            console.log(`   任务ID: ${taskId}`);
            
            // 轮询任务状态
            let attempts = 0;
            const maxAttempts = 60; // 最多等待5分钟
            let finalStatus = null;
            
            while (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
                
                try {
                    const statusResponse = await axios.get(
                        `${this.baseUrl}/webhook/status-query?task_id=${taskId}`,
                        { timeout: 10000 }
                    );
                    
                    if (statusResponse.data.success) {
                        const status = statusResponse.data.status;
                        const progress = statusResponse.data.progress_percentage;
                        
                        console.log(`   进度: ${progress}% - ${status}`);
                        
                        if (status === 'COMPLETED') {
                            finalStatus = statusResponse.data;
                            break;
                        } else if (status === 'FAILED') {
                            finalStatus = statusResponse.data;
                            break;
                        }
                    }
                } catch (error) {
                    console.log(`   状态查询失败: ${error.message}`);
                }
                
                attempts++;
            }
            
            const duration = Date.now() - start;
            
            if (finalStatus && finalStatus.status === 'COMPLETED') {
                const stats = finalStatus.statistics;
                this.logResult(
                    '完整工作流测试',
                    true,
                    `工作流执行成功，分析了${stats.total_repos}个项目，推荐${stats.recommended_count}个`,
                    duration,
                    { 
                        total_repos: stats.total_repos,
                        recommended_count: stats.recommended_count,
                        average_score: stats.average_score
                    }
                );
                return true;
            } else if (finalStatus && finalStatus.status === 'FAILED') {
                this.logResult(
                    '完整工作流测试',
                    false,
                    '工作流执行失败',
                    duration,
                    { error: finalStatus.message }
                );
                return false;
            } else {
                this.logResult(
                    '完整工作流测试',
                    false,
                    '工作流执行超时',
                    duration,
                    { attempts, maxAttempts }
                );
                return false;
            }
            
        } catch (error) {
            this.logResult(
                '完整工作流测试',
                false,
                '工作流测试失败',
                0,
                { error: error.message }
            );
            return false;
        }
    }

    // 测试错误处理机制
    async testErrorHandling() {
        console.log('\n🚨 错误处理机制测试...');
        
        try {
            // 模拟一个错误情况
            const errorRequest = {
                task_id: 'test_task_123',
                error_type: 'RATE_LIMIT',
                error_message: 'API rate limit exceeded',
                retry_count: 1,
                max_retries: 3
            };
            
            const start = Date.now();
            const response = await axios.post(
                `${this.baseUrl}/webhook/error-handler`,
                errorRequest,
                { timeout: 30000 }
            );
            const duration = Date.now() - start;
            
            if (response.data.success && response.data.status === 'RETRY_SCHEDULED') {
                this.logResult(
                    '错误处理机制',
                    true,
                    '错误处理机制正常工作',
                    duration,
                    { retry_scheduled: true }
                );
                return true;
            } else {
                this.logResult(
                    '错误处理机制',
                    false,
                    '错误处理机制异常',
                    duration,
                    { response: response.data }
                );
                return false;
            }
        } catch (error) {
            this.logResult(
                '错误处理机制',
                false,
                '错误处理测试失败',
                0,
                { error: error.message }
            );
            return false;
        }
    }

    // 性能测试
    async testPerformance() {
        console.log('\n⚡ 性能测试...');
        
        const performanceTests = [
            {
                name: '系统状态查询性能',
                url: `${this.baseUrl}/webhook/system-status`,
                expectedTime: 1000 // 1秒内
            },
            {
                name: '工作流统计查询性能',
                url: `${this.baseUrl}/webhook/workflow-stats`,
                expectedTime: 2000 // 2秒内
            }
        ];
        
        let allPassed = true;
        
        for (const test of performanceTests) {
            try {
                const start = Date.now();
                const response = await axios.get(test.url, { timeout: 10000 });
                const duration = Date.now() - start;
                
                const passed = duration <= test.expectedTime && response.data.success;
                
                this.logResult(
                    test.name,
                    passed,
                    passed ? 
                        `响应时间${duration}ms，符合预期` : 
                        `响应时间${duration}ms，超过预期${test.expectedTime}ms`,
                    duration,
                    { expected: test.expectedTime, actual: duration }
                );
                
                if (!passed) allPassed = false;
                
            } catch (error) {
                this.logResult(
                    test.name,
                    false,
                    '性能测试失败',
                    0,
                    { error: error.message }
                );
                allPassed = false;
            }
        }
        
        return allPassed;
    }

    // 生成测试报告
    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const successRate = ((passedTests / totalTests) * 100).toFixed(1);
        const totalDuration = Date.now() - this.startTime;
        
        const report = {
            summary: {
                total_tests: totalTests,
                passed_tests: passedTests,
                failed_tests: failedTests,
                success_rate: `${successRate}%`,
                total_duration: `${totalDuration}ms`,
                test_date: new Date().toISOString()
            },
            results: this.testResults
        };
        
        // 保存报告到文件
        const reportPath = path.join(__dirname, `test-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n📊 测试报告');
        console.log('='.repeat(50));
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过测试: ${passedTests}`);
        console.log(`失败测试: ${failedTests}`);
        console.log(`成功率: ${successRate}%`);
        console.log(`总耗时: ${totalDuration}ms`);
        console.log(`报告文件: ${reportPath}`);
        
        if (failedTests > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults
                .filter(r => !r.success)
                .forEach(r => {
                    console.log(`  - ${r.test}: ${r.message}`);
                });
        }
        
        return report;
    }

    // 运行所有测试
    async runAllTests(options = {}) {
        console.log('🚀 开始系统测试...');
        console.log(`测试目标: ${this.baseUrl}`);
        
        const tests = [
            () => this.testSystemHealth(),
            () => this.testGitHubAPI(),
            () => this.testAIAnalysis(),
            () => this.testErrorHandling()
        ];
        
        if (options.full) {
            tests.push(() => this.testCompleteWorkflow());
        }
        
        if (options.performance) {
            tests.push(() => this.testPerformance());
        }
        
        // 执行所有测试
        for (const test of tests) {
            try {
                await test();
            } catch (error) {
                console.error(`测试执行错误: ${error.message}`);
            }
        }
        
        // 生成报告
        const report = this.generateReport();
        
        // 返回测试是否全部通过
        return report.summary.failed_tests === 0;
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const options = {
        full: args.includes('--full'),
        performance: args.includes('--performance'),
        apiOnly: args.includes('--api-only')
    };
    
    const tester = new SystemTester();
    
    try {
        const allPassed = await tester.runAllTests(options);
        
        console.log('\n' + '='.repeat(50));
        if (allPassed) {
            console.log('🎉 所有测试通过！系统运行正常。');
            process.exit(0);
        } else {
            console.log('⚠️  部分测试失败，请检查系统配置。');
            process.exit(1);
        }
    } catch (error) {
        console.error('测试执行失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = SystemTester;

# GitHub项目商业化分析系统

一个基于n8n构建的自动化工作流系统，用于分析GitHub开源项目的商业化可行性，并生成专业的评估报告。

## 🎯 系统概述

本系统通过AI驱动的多维度分析，自动评估GitHub开源项目的商业化潜力，为投资决策、技术选型和商业规划提供数据支持。

### 核心功能

- 🔍 **智能项目搜索**: 基于多种条件筛选GitHub项目
- 🤖 **AI深度分析**: 技术、商业、风险三维度专业评估
- 📊 **多格式报告**: Excel、PDF、JSON等多种输出格式
- 📈 **实时监控**: 工作流执行进度和状态跟踪
- 🔄 **错误处理**: 完善的重试机制和异常处理
- 📧 **智能通知**: 多渠道状态通知和告警

### 分析维度

#### 技术分析 (30%权重)
- 代码质量评估
- 架构设计分析
- 技术栈现代性
- 文档完整性
- 维护活跃度

#### 商业分析 (40%权重)
- 市场需求评估
- 竞争环境分析
- 盈利模式设计
- 目标客户识别
- 增长潜力评估

#### 风险评估 (30%权重)
- 技术风险
- 法律合规风险
- 运营维护风险
- 市场竞争风险
- 团队依赖风险

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户输入表单   │───▶│   主工作流引擎   │───▶│   报告生成器     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GitHub API    │◀───│   数据获取模块   │───▶│   AI分析引擎     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   错误处理器     │◀───│   进度监控器     │───▶│   通知系统       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- n8n >= 1.0.0
- Node.js >= 18.0.0
- 内存 >= 4GB
- 存储空间 >= 10GB

### 安装步骤

1. **安装n8n**
```bash
npm install -g n8n
```

2. **启动n8n**
```bash
n8n start
```

3. **访问Web界面**
```
http://localhost:5678
```

4. **导入工作流**
   - 下载本项目的工作流文件
   - 在n8n界面中导入工作流
   - 配置必要的凭据和环境变量

### 必需配置

#### GitHub API凭据
```json
{
  "name": "GitHub API",
  "type": "githubApi",
  "data": {
    "accessToken": "your_github_token",
    "server": "https://api.github.com"
  }
}
```

#### OpenAI API凭据
```json
{
  "name": "OpenAI",
  "type": "openAi",
  "data": {
    "apiKey": "your_openai_api_key"
  }
}
```

#### 通知配置（可选）
```json
{
  "slack_webhook_url": "https://hooks.slack.com/...",
  "notification_email": "<EMAIL>"
}
```

## 📖 使用指南

### 基本使用流程

1. **访问分析表单**
   ```
   http://localhost:5678/form/github-analysis
   ```

2. **填写分析条件**
   - 选择编程语言
   - 设置仓库排序方式
   - 配置数量限制和评分阈值
   - 选择许可证类型

3. **提交并等待分析**
   - 系统自动获取GitHub数据
   - AI进行多维度分析
   - 生成综合评估报告

4. **查看分析结果**
   - Excel详细报告
   - PDF可视化报告
   - JSON结构化数据

### 高级功能

#### 批量分析
```bash
curl -X POST http://localhost:5678/webhook/batch-analysis \
  -H "Content-Type: application/json" \
  -d '{
    "projects": ["owner/repo1", "owner/repo2"],
    "analysis_depth": "detailed"
  }'
```

#### 状态查询
```bash
curl "http://localhost:5678/webhook/status-query?task_id=your_task_id"
```

#### 错误重试
```bash
curl -X POST http://localhost:5678/webhook/error-handler \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "failed_task_id",
    "error_type": "RATE_LIMIT",
    "retry_count": 1
  }'
```

## 🔧 配置说明

### 工作流配置

#### 主工作流参数
```javascript
{
  "batchSize": 3,           // 批处理大小
  "maxRetries": 3,          // 最大重试次数
  "rateLimitDelay": 2000,   // API限流延迟(ms)
  "analysisTimeout": 3600   // 分析超时时间(s)
}
```

#### AI分析配置
```javascript
{
  "model": "gpt-4",
  "temperature": 0.7,
  "maxTokens": 4000,
  "responseFormat": "json_object"
}
```

### 评分权重配置

可以通过修改主工作流中的权重参数来调整评分算法：

```javascript
const weights = {
  technical: 0.3,    // 技术维度权重
  business: 0.4,     // 商业维度权重
  risk: 0.3          // 风险维度权重
};
```

### 输出格式配置

#### Excel报告配置
```javascript
{
  "includeCharts": true,
  "includeRawData": false,
  "maxRowsPerSheet": 1000,
  "dateFormat": "yyyy-MM-dd"
}
```

#### PDF报告配置
```javascript
{
  "pageSize": "A4",
  "orientation": "portrait",
  "includeCharts": true,
  "watermark": "CONFIDENTIAL"
}
```

## 📊 输出说明

### Excel报告结构

1. **推荐项目列表** - 主要分析结果
2. **统计汇总** - 整体分析统计
3. **技术栈分布** - 编程语言分布
4. **许可证分布** - 开源许可证统计

### PDF报告内容

1. **执行摘要** - 关键指标和发现
2. **数据分析** - 可视化图表
3. **顶级推荐** - 最佳项目详情
4. **战略建议** - 商业化建议

### JSON数据格式

```json
{
  "summary": {
    "task_id": "20240718_143022_abc123",
    "total_analyzed": 10,
    "recommended_count": 3,
    "average_score": 6.8
  },
  "projects": [
    {
      "project": {
        "name": "example-project",
        "url": "https://github.com/owner/repo",
        "language": "JavaScript",
        "license": "MIT"
      },
      "scores": {
        "final_score": 8.2,
        "tech_score": 8.0,
        "business_score": 8.5,
        "risk_score": 8.0
      },
      "analysis": {
        "technical": {...},
        "business": {...},
        "risk": {...}
      }
    }
  ]
}
```

## 🔍 监控和调试

### 日志查看

n8n执行日志：
```bash
tail -f ~/.n8n/logs/n8n.log
```

工作流执行历史：
- 访问 n8n Web界面
- 进入 "Executions" 页面
- 查看详细执行记录

### 性能监控

#### 关键指标
- API调用频率和限额使用
- 工作流执行时间
- 内存和CPU使用率
- 错误率和重试次数

#### 监控端点
```bash
# 系统状态
curl http://localhost:5678/webhook/system-status

# 工作流统计
curl http://localhost:5678/webhook/workflow-stats

# API使用情况
curl http://localhost:5678/webhook/api-usage
```

## 🚨 故障排除

### 常见问题

#### 1. GitHub API限流
**症状**: 403错误，提示rate limit exceeded
**解决方案**:
- 检查API token权限
- 增加请求间隔时间
- 使用GitHub App认证获得更高限额

#### 2. AI分析超时
**症状**: OpenAI API调用超时
**解决方案**:
- 增加超时时间设置
- 减少分析内容长度
- 检查网络连接状态

#### 3. 内存不足
**症状**: 工作流执行中断，内存错误
**解决方案**:
- 减少批处理大小
- 增加系统内存
- 优化数据处理逻辑

### 错误代码说明

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| RATE_LIMIT | API限流 | 等待或使用更高级别的API |
| AUTH_ERROR | 认证失败 | 检查API密钥配置 |
| NETWORK_ERROR | 网络错误 | 检查网络连接 |
| TIMEOUT | 请求超时 | 增加超时时间 |
| INVALID_DATA | 数据格式错误 | 检查输入数据格式 |

## 📞 技术支持

### 联系方式
- 邮箱: <EMAIL>
- 文档: https://docs.github-analyzer.com
- 问题反馈: https://github.com/your-org/github-analyzer/issues

### 社区资源
- 用户手册: [链接]
- 视频教程: [链接]
- 最佳实践: [链接]
- FAQ: [链接]

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献指南

欢迎贡献代码和建议！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

---

**版本**: v1.0.0  
**更新时间**: 2024-07-18  
**维护者**: GitHub项目分析团队

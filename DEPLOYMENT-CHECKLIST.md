# GitHub项目商业化分析系统 - 部署检查清单

## 📋 部署前准备

### 系统要求检查
- [ ] **操作系统**: Linux/macOS/Windows (推荐Ubuntu 20.04+)
- [ ] **Node.js版本**: >= 18.0.0
- [ ] **内存**: >= 4GB RAM (推荐8GB+)
- [ ] **存储空间**: >= 10GB 可用空间
- [ ] **网络**: 稳定的互联网连接，能访问GitHub和OpenAI API

### 依赖安装
- [ ] 安装Node.js和npm
- [ ] 全局安装n8n: `npm install -g n8n`
- [ ] 克隆项目代码
- [ ] 安装项目依赖: `npm install`

## 🔑 API凭据配置

### GitHub API
- [ ] 创建GitHub Personal Access Token
- [ ] 确保token具有以下权限：
  - [ ] `public_repo` - 访问公共仓库
  - [ ] `read:org` - 读取组织信息
  - [ ] `read:user` - 读取用户信息
- [ ] 在n8n中配置GitHub API凭据
- [ ] 测试API连接: `curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user`

### OpenAI API
- [ ] 获取OpenAI API密钥
- [ ] 确保账户有足够余额
- [ ] 在n8n中配置OpenAI凭据
- [ ] 测试API连接: `curl https://api.openai.com/v1/models -H "Authorization: Bearer YOUR_KEY"`

### 可选配置
- [ ] Slack Webhook URL (用于通知)
- [ ] 邮件服务配置 (用于通知)
- [ ] 数据库连接 (如果使用外部数据库)

## 🔧 工作流导入

### 主要工作流
- [ ] 导入主分析工作流: `GitHub 项目商业化分析 - 完整系统`
- [ ] 导入错误处理工作流: `GitHub分析系统 - 错误处理与重试机制`
- [ ] 导入进度监控工作流: `GitHub分析系统 - 进度监控与状态反馈`
- [ ] 导入报告生成工作流: `GitHub分析系统 - 高级报告生成器`

### 工作流配置
- [ ] 检查所有工作流的凭据配置
- [ ] 验证webhook URL设置
- [ ] 调整批处理大小和超时设置
- [ ] 配置错误重试参数

## 🚀 启动服务

### n8n服务启动
- [ ] 启动n8n: `n8n start`
- [ ] 验证服务运行: 访问 `http://localhost:5678`
- [ ] 检查所有工作流状态为"非活跃"状态

### 服务配置
- [ ] 配置环境变量
- [ ] 设置日志级别和轮转
- [ ] 配置进程管理 (PM2/systemd)

## ✅ 功能测试

### 基础功能测试
- [ ] 运行系统健康检查: `npm run health-check`
- [ ] 测试GitHub API连接
- [ ] 测试OpenAI API连接
- [ ] 验证工作流可以正常触发

### 端到端测试
- [ ] 运行基础测试: `npm test`
- [ ] 运行完整测试: `npm run test:full`
- [ ] 运行性能测试: `npm run test:performance`
- [ ] 验证所有测试通过

### 手动验证
- [ ] 通过Web表单提交小规模分析任务
- [ ] 验证分析结果的准确性
- [ ] 检查生成的报告格式
- [ ] 测试错误处理机制

## 📊 监控设置

### 日志监控
- [ ] 配置日志文件路径
- [ ] 设置日志轮转策略
- [ ] 配置错误日志告警

### 性能监控
- [ ] 设置系统资源监控
- [ ] 配置API使用量监控
- [ ] 设置工作流执行时间监控

### 告警配置
- [ ] 配置系统故障告警
- [ ] 设置API限额告警
- [ ] 配置性能异常告警

## 🔒 安全配置

### 访问控制
- [ ] 配置n8n用户认证
- [ ] 设置webhook访问限制
- [ ] 配置HTTPS (生产环境)

### 数据安全
- [ ] 配置敏感数据加密
- [ ] 设置API密钥轮换策略
- [ ] 配置数据备份策略

## 📈 生产环境优化

### 性能优化
- [ ] 调整Node.js内存限制
- [ ] 优化批处理参数
- [ ] 配置负载均衡 (如需要)

### 可靠性配置
- [ ] 配置自动重启机制
- [ ] 设置健康检查端点
- [ ] 配置故障转移策略

## 🔄 备份和恢复

### 备份策略
- [ ] 配置工作流定期备份
- [ ] 设置凭据备份
- [ ] 配置数据库备份 (如适用)

### 恢复测试
- [ ] 测试工作流恢复流程
- [ ] 验证凭据恢复
- [ ] 测试完整系统恢复

## 📚 文档和培训

### 文档准备
- [ ] 部署文档完整性检查
- [ ] API文档可访问性验证
- [ ] 故障排除指南准备

### 团队培训
- [ ] 系统操作培训
- [ ] 故障排除培训
- [ ] 监控和维护培训

## 🎯 上线检查

### 最终验证
- [ ] 所有测试通过
- [ ] 监控系统正常工作
- [ ] 备份策略已实施
- [ ] 团队已完成培训

### 上线准备
- [ ] 制定上线计划
- [ ] 准备回滚方案
- [ ] 通知相关团队
- [ ] 准备上线后监控

## 📞 支持联系

### 技术支持
- [ ] 确认技术支持联系方式
- [ ] 准备问题上报流程
- [ ] 配置紧急联系机制

### 社区资源
- [ ] 加入用户社区
- [ ] 订阅更新通知
- [ ] 准备反馈渠道

---

## 🚨 常见部署问题

### 端口冲突
**问题**: 端口5678被占用
**解决**: 
```bash
# 查找占用进程
lsof -i :5678
# 或使用其他端口启动
N8N_PORT=5679 n8n start
```

### 内存不足
**问题**: JavaScript heap out of memory
**解决**:
```bash
# 增加Node.js内存限制
NODE_OPTIONS="--max-old-space-size=4096" n8n start
```

### API认证失败
**问题**: GitHub/OpenAI API认证失败
**解决**:
1. 验证API密钥正确性
2. 检查API权限设置
3. 确认账户余额充足

### 工作流导入失败
**问题**: 工作流JSON格式错误
**解决**:
1. 验证JSON格式正确性
2. 检查n8n版本兼容性
3. 逐个导入工作流进行排查

---

**部署完成后，请保存此检查清单作为运维参考文档。**

**版本**: v1.0.0  
**更新时间**: 2024-07-18  
**维护者**: GitHub项目分析团队

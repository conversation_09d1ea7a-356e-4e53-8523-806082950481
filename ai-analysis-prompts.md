# GitHub 项目商业化分析 - AI 提示词模板

## 技术分析提示词 (Technical Analysis Prompt)

```
你是一个资深的技术架构师和代码质量专家，拥有15年以上的软件开发和技术评估经验。请对以下GitHub开源项目进行专业的技术分析评估。

### 分析维度和评分标准：

#### 1. 代码质量评估 (25分)
- **项目活跃度** (0-5分): 基于stars、forks、contributors数量
  - 5分: Stars>10k, Forks>1k, Contributors>100
  - 4分: Stars>5k, Forks>500, Contributors>50
  - 3分: Stars>1k, Forks>100, Contributors>20
  - 2分: Stars>100, Forks>20, Contributors>5
  - 1分: 其他情况

- **维护频率** (0-5分): 基于最后更新时间
  - 5分: 7天内更新
  - 4分: 30天内更新
  - 3分: 90天内更新
  - 2分: 180天内更新
  - 1分: 超过180天

- **社区参与度** (0-5分): 基于issue处理、PR活跃度
  - 评估开放issue数量与项目规模的比例
  - 评估社区响应速度和质量

- **代码组织结构** (0-5分): 基于项目结构和文档
  - 评估目录结构的合理性
  - 评估代码模块化程度

- **测试覆盖率推测** (0-5分): 基于项目特征推测
  - 是否有测试目录
  - 是否有CI/CD配置
  - 项目类型对测试的重视程度

#### 2. 架构设计评估 (25分)
- **技术栈选择** (0-10分): 评估技术选择的合理性和现代性
- **可扩展性设计** (0-10分): 基于项目描述和结构评估
- **性能考虑** (0-5分): 评估性能优化的考虑

#### 3. 技术栈现代性 (20分)
- **编程语言现代性** (0-10分): 评估所用语言的主流程度和发展趋势
- **依赖管理** (0-5分): 评估依赖的现代性和安全性
- **开发工具链** (0-5分): 评估构建工具、包管理等

#### 4. 文档完整性 (15分)
- **README质量** (0-10分): 评估README的完整性和清晰度
- **API文档** (0-5分): 评估是否有完整的API文档

#### 5. 维护活跃度 (15分)
- **更新频率** (0-8分): 评估项目更新的规律性
- **问题响应** (0-7分): 评估issue和PR的处理效率

### 输出要求：
请以JSON格式输出分析结果，包含：
- tech_score: 技术评分(1-10，保留1位小数)
- code_quality: 代码质量评估(详细字符串，150-200字)
- architecture: 架构评估(详细字符串，150-200字)
- tech_stack: 技术栈评估(详细字符串，100-150字)
- documentation: 文档评估(详细字符串，100-150字)
- maintenance: 维护状态(详细字符串，100-150字)
- strengths: 技术优势(数组，3-5个要点)
- weaknesses: 技术弱点(数组，2-4个要点)
- recommendations: 技术改进建议(数组，3-5个具体建议)
- scalability_assessment: 可扩展性评估(字符串，100字)
- performance_indicators: 性能指标评估(字符串，100字)

### 项目信息：
**项目名称**: {{project_name}}
**项目描述**: {{project_description}}
**项目地址**: {{project_url}}
**主要语言**: {{primary_language}}
**许可证**: {{license}}
**项目标签**: {{topics}}

**项目指标**:
- Stars数量: {{stars}}
- Forks数量: {{forks}}
- 贡献者数: {{contributors}}
- 开放Issue数: {{open_issues}}
- 最后更新距今: {{days_since_update}}天
- 是否有Wiki: {{has_wiki}}
- 是否有Pages: {{has_pages}}

**README预览**:
{{readme_preview}}

**主要贡献者**:
{{top_contributors}}
```

## 商业分析提示词 (Business Analysis Prompt)

```
你是一个具有丰富经验的商业分析师和投资顾问，专门评估技术项目的商业化潜力。请对以下GitHub开源项目进行深入的商业可行性分析。

### 分析框架和评分标准：

#### 1. 市场需求分析 (30分)
- **问题严重性** (0-10分): 项目解决的问题是否是真实痛点
- **市场规模** (0-10分): 目标市场的规模和增长潜力
- **用户基础** (0-10分): 基于stars、forks评估用户需求强度

#### 2. 竞争环境分析 (25分)
- **竞争激烈程度** (0-10分): 同类产品的竞争情况
- **差异化优势** (0-10分): 项目的独特价值主张
- **进入壁垒** (0-5分): 技术门槛和资源要求

#### 3. 盈利模式评估 (25分)
- **变现路径清晰度** (0-10分): 商业模式的可行性
- **收入多样性** (0-10分): 多种收入来源的可能性
- **定价能力** (0-5分): 产品的定价空间

#### 4. 目标客户分析 (20分)
- **客户群体明确性** (0-10分): 目标客户的清晰度
- **付费意愿** (0-10分): 客户的付费能力和意愿

### 重点分析领域：

#### A. 市场机会评估
- 分析项目所在的技术领域的市场趋势
- 评估目标用户群体的规模和增长潜力
- 识别未被满足的市场需求

#### B. 商业模式设计
- SaaS订阅模式的可行性
- 企业版/专业版的差异化策略
- 技术服务和咨询的收入潜力
- 生态系统和平台化的机会
- 开源+商业双轨模式

#### C. 竞争优势分析
- 技术壁垒和专利保护
- 网络效应和用户粘性
- 品牌认知度和社区影响力
- 团队执行能力

#### D. 风险与挑战
- 技术替代风险
- 市场变化风险
- 竞争加剧风险
- 监管政策风险

### 输出要求：
请以JSON格式输出分析结果，包含：
- business_score: 商业评分(1-10，保留1位小数)
- market_demand: 市场需求评估(详细字符串，200-250字)
- competition: 竞争分析(详细字符串，200-250字)
- monetization_models: 可能的盈利模式(数组，5-8个具体模式)
- target_customers: 目标客户群体(数组，4-6个客户类型)
- growth_potential: 增长潜力评估(详细字符串，150-200字)
- entry_barrier: 技术门槛评估(详细字符串，100-150字)
- commercialization_suggestions: 商业化建议(数组，5-7个具体建议)
- market_size: 市场规模估计(字符串，100-150字)
- competitive_advantages: 竞争优势(数组，4-6个优势点)
- revenue_potential: 收入潜力评估(字符串，100-150字)
- go_to_market_strategy: 市场进入策略(数组，3-5个策略)

### 项目信息：
**项目名称**: {{project_name}}
**项目描述**: {{project_description}}
**主要语言**: {{primary_language}}
**许可证**: {{license}}
**项目标签**: {{topics}}

**市场表现**:
- Stars数量: {{stars}}
- Forks数量: {{forks}}
- 贡献者数: {{contributors}}
- 社区活跃度: {{days_since_update}}天前更新

**技术特点**:
- 主要编程语言: {{primary_language}}
- 开源许可证: {{license}}
- 项目标签: {{topics}}

**项目详情**:
{{readme_preview}}
```

## 风险评估提示词 (Risk Assessment Prompt)

```
你是一个专业的风险评估专家和投资尽职调查顾问，拥有丰富的技术项目风险识别和评估经验。请对以下GitHub开源项目进行全面的商业化风险评估。

### 风险评估框架和评分标准：

#### 1. 技术风险评估 (25分)
- **技术债务风险** (0-5分): 代码质量和维护成本
- **依赖风险** (0-5分): 第三方依赖的稳定性和安全性
- **性能风险** (0-5分): 可扩展性和性能瓶颈
- **技术过时风险** (0-5分): 技术栈的生命周期和替代风险
- **安全漏洞风险** (0-5分): 潜在的安全问题

#### 2. 法律合规风险 (20分)
- **许可证兼容性** (0-8分): 开源许可证的商业化限制
- **知识产权风险** (0-7分): 专利侵权和版权问题
- **数据保护合规** (0-5分): GDPR、CCPA等法规遵循

#### 3. 运营维护风险 (20分)
- **维护成本** (0-8分): 长期维护和升级的资源需求
- **技术支持成本** (0-7分): 客户服务和技术支持负担
- **人才招聘风险** (0-5分): 相关技能人才的可获得性

#### 4. 市场和竞争风险 (20分)
- **市场需求变化** (0-8分): 市场趋势变化的影响
- **竞争威胁** (0-7分): 大公司进入或技术替代
- **客户集中度风险** (0-5分): 客户流失的影响

#### 5. 团队和组织风险 (15分)
- **核心开发者依赖** (0-8分): 对关键人员的依赖程度
- **团队稳定性** (0-7分): 开发团队的连续性和稳定性

### 详细风险分析领域：

#### A. 技术风险深度分析
- **代码质量风险**: 基于项目规模、复杂度、测试覆盖率评估
- **架构风险**: 评估架构的可维护性和可扩展性
- **依赖管理风险**: 分析第三方库的安全性和维护状态
- **性能瓶颈**: 识别潜在的性能限制和扩展障碍
- **安全威胁**: 评估常见安全漏洞的风险

#### B. 商业化转型风险
- **开源社区关系**: 商业化对开源社区的影响
- **许可证变更风险**: 许可证政策变化的法律风险
- **竞争对手反应**: 商业化可能引发的竞争反应
- **用户接受度**: 付费模式的用户接受程度

#### C. 运营风险评估
- **资源投入需求**: 商业化所需的人力、资金投入
- **基础设施成本**: 服务器、CDN、监控等运营成本
- **客户服务负担**: 技术支持和客户成功的资源需求
- **合规成本**: 各种法规遵循的成本和复杂度

#### D. 财务风险分析
- **现金流风险**: 收入模式的稳定性和可预测性
- **投资回报风险**: ROI实现的时间和确定性
- **融资风险**: 后续融资的难易程度
- **汇率风险**: 国际化业务的汇率波动影响

### 风险缓解策略建议：
针对识别出的每个高风险领域，提供具体的缓解措施和预防策略。

### 输出要求：
请以JSON格式输出分析结果，包含：
- risk_score: 风险评分(1-10，分数越高风险越低，保留1位小数)
- technical_risks: 技术风险(数组，4-6个具体风险)
- legal_risks: 法律风险(数组，3-5个具体风险)
- operational_risks: 运营风险(数组，4-6个具体风险)
- market_risks: 市场风险(数组，3-5个具体风险)
- team_risks: 团队风险(数组，2-4个具体风险)
- maintenance_cost: 维护成本评估(详细字符串，150-200字)
- team_dependency: 团队依赖性评估(详细字符串，100-150字)
- mitigation_strategies: 风险缓解策略(数组，6-8个具体策略)
- overall_risk_level: 总体风险等级(字符串：低风险/中等风险/高风险)
- critical_risk_factors: 关键风险因素(数组，3-5个最重要的风险)
- risk_timeline: 风险时间线评估(字符串，100-150字)

### 项目基本信息：
**项目名称**: {{project_name}}
**项目描述**: {{project_description}}
**主要语言**: {{primary_language}}
**许可证**: {{license}}
**项目地址**: {{project_url}}

**项目状态**:
- Stars数量: {{stars}}
- Forks数量: {{forks}}
- 贡献者数: {{contributors}}
- 开放Issue数: {{open_issues}}
- 最后更新: {{days_since_update}}天前

**团队信息**:
- 主要贡献者: {{top_contributors}}
- 贡献者总数: {{contributors}}

**技术栈**:
- 主要编程语言: {{primary_language}}
- 开源许可证: {{license}}
- 相关技术标签: {{topics}}
```

## 使用说明

### 1. 提示词使用方法

- 将模板中的 `{{变量名}}` 替换为实际的项目数据
- 确保所有必需的项目信息都已填充
- 根据具体需求调整评分标准和权重

### 2. 评分标准说明

- 技术分析: 重点关注代码质量、架构设计、技术现代性
- 商业分析: 重点关注市场需求、盈利模式、竞争优势
- 风险评估: 重点关注各类风险的识别和量化

### 3. 输出格式要求

- 所有输出必须为有效的 JSON 格式
- 评分使用 1-10 分制，保留 1 位小数
- 文字描述要求具体、专业、有建设性

### 4. 质量控制

- 确保分析的客观性和专业性
- 避免过于乐观或悲观的评估
- 提供具体可行的建议和策略
